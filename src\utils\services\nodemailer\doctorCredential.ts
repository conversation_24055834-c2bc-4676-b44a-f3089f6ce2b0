import nodemailer from "nodemailer";
import dotenv from "dotenv";

dotenv.config();

// Create a reusable transporter object
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || "smtp.gmail.com",
  port: parseInt(process.env.EMAIL_PORT || "587"),
  secure: process.env.EMAIL_SECURE === "true",
  auth: {
    user: process.env.EMAIL_USER || "<EMAIL>",
    pass: process.env.EMAIL_PASSWORD || "hzkvvyyytvarnuhy",
  },
});

interface DoctorCredentials {
  email: string;
  password: string;
  name: string;
}

interface PasswordResetData {
  email: string;
  name: string;
  password: string;
}

export const sendDoctorCredentialEmail = async (doctorData: DoctorCredentials) => {
  const { email, password, name } = doctorData;
  
  const mailOptions = {
    from: process.env.EMAIL_FROM || "<EMAIL>",
    to: email,
    subject: "Your Doctor Account Credentials",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2 style="color: #333;">Welcome to Our Orthodontic Platform</h2>
        <p>Hello ${name},</p>
        <p>Your doctor account has been created by an administrator. Below are your login credentials:</p>
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Password:</strong> ${password}</p>
        </div>
        <p>Please log in using these credentials and change your password immediately for security purposes.</p>
        <p>If you didn't expect this email, please contact our administrator.</p>
        <p>Best regards,<br>Orthodontic Platform Team</p>
      </div>
    `,
  };

  await transporter.sendMail(mailOptions);
};

export const sendPasswordResetEmail = async (resetData: PasswordResetData) => {
  const { email, name, password } = resetData;

  const mailOptions = {
    from: process.env.EMAIL_FROM || "<EMAIL>",
    to: email,
    subject: "Your New Password",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2 style="color: #333;">Password Reset Successful</h2>
        <p>Hello ${name},</p>
        <p>Your password has been successfully reset. Here are your new login credentials:</p>
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p><strong>Password:</strong> ${password}</p>
        </div>
        <p>Please change your password after logging in for security reasons.</p>
        <p>If you didn't request this reset, please contact support immediately.</p>
        <p>Best regards,<br>Orthodontic Platform Team</p>
      </div>
    `,
  };

  await transporter.sendMail(mailOptions);
};