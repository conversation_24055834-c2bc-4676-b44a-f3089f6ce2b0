import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('plans', (table) => {
    table
      .enu('type', ['aligner', 'retainer'])
      .notNullable()
      .defaultTo('aligner');
    table.integer('duration_years').unsigned().nullable();
    table.timestamp('expiration_date').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('plans', (table) => {
    table.dropColumn('type');
    table.dropColumn('duration_years');
    table.dropColumn('expiration_date');
  });
}
