import type { K<PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(TABLE.USERS, (table) => {
    table
      .integer("specialist_id")
      .unsigned()
      .nullable()
      .references("id")
      .inTable(TABLE.USERS)
      .onDelete("SET NULL");
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(TABLE.USERS, (table) => {
    table.dropColumn("specialist_id");
  });
}