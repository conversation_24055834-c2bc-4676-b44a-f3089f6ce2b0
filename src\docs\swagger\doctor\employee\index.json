{"paths": {"/doctor/employees": {"post": {"summary": "Create a new employee under a doctor", "tags": ["Doctor Management"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe", "description": "Optional. Must be unique if provided."}, "role_id": {"type": "integer", "example": 3, "description": "Optional. Role ID for the employee. If not provided, default employee role will be assigned."}}, "required": ["first_name", "last_name", "email"]}}}}, "responses": {"201": {"description": "Employee created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 201}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Employee created successfully and credentials sent via email"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 5}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe"}, "role_id": {"type": "integer", "example": 3}, "role": {"type": "string", "example": "employee"}}}}}}}}, "400": {"description": "Bad request - validation error or duplicate email/username", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 400}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Email already exists"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 401}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized! please login first."}}}}}}, "403": {"description": "Forbidden - User is not a doctor", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 403}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Only doctors can create employees"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 500}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}}}}}}}}, "get": {"summary": "Get all employees under a doctor", "tags": ["Doctor Management"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Employees retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Employees retrieved successfully"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 5}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe"}, "profile_image": {"type": "string", "example": "http://localhost:5000/user/profile-123.jpg", "nullable": true}, "role": {"type": "string", "example": "employee"}}}}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 401}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized! please login first."}}}}}}, "403": {"description": "Forbidden - User is not a doctor", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 403}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Only doctors can view their employees"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 500}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}}}}}}}}}, "/doctor/employees/{employeeId}": {"put": {"summary": "Update an employee's information", "tags": ["Doctor Management"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "employeeId", "required": true, "schema": {"type": "integer"}, "description": "ID of the employee to update"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe"}, "role_id": {"type": "integer", "example": 3, "description": "Optional. Role ID to assign to the employee. Cannot be admin, superadmin, or doctor roles."}}}}}}, "responses": {"200": {"description": "Employee updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Employee updated successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 5}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe"}, "role_id": {"type": "integer", "example": 3}, "role": {"type": "string", "example": "employee"}}}}}}}}, "400": {"description": "Bad request - validation error, duplicate email/username, or no fields to update", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 400}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Email already exists"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 401}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized! please login first."}}}}}}, "403": {"description": "Forbidden - User is not a doctor", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 403}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Only doctors can update their employees"}}}}}}, "404": {"description": "Employee not found or doesn't belong to the doctor", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 404}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Employee not found or doesn't belong to you"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 500}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}}}}}}}}, "delete": {"summary": "Delete an employee", "tags": ["Doctor Management"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "employeeId", "required": true, "schema": {"type": "integer"}, "description": "ID of the employee to delete"}], "responses": {"200": {"description": "Employee deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Employee deleted successfully"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 401}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized! please login first."}}}}}}, "403": {"description": "Forbidden - User is not a doctor", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 403}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Only doctors can delete their employees"}}}}}}, "404": {"description": "Employee not found or doesn't belong to the doctor", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 404}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Employee not found or doesn't belong to you"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 500}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}}}}}}}}}}}