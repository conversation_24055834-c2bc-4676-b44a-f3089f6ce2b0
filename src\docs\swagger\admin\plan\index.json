{"paths": {"/admin/plan": {"post": {"summary": "Create Plan", "tags": ["Admin Plan"], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "Basic Aligner"}, "type": {"type": "string", "example": "aligner"}, "duration_years": {"type": "string", "example": "2"}}, "required": ["name", "type"]}}}}, "responses": {"201": {"description": "Plan created successfully"}, "400": {"description": "Validation error"}, "500": {"description": "Internal server error"}}}, "get": {"summary": "List Plans", "tags": ["Admin Plan"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "limit", "in": "query", "schema": {"type": "string", "example": "10"}}], "responses": {"200": {"description": "Plans fetched successfully"}, "500": {"description": "Internal server error"}}}}, "/admin/plan/{id}": {"get": {"summary": "Get Plan by ID", "tags": ["Admin Plan"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "example": "123"}}], "responses": {"200": {"description": "Plan fetched successfully"}, "404": {"description": "Plan not found"}}}, "patch": {"summary": "Update Plan", "tags": ["Admin Plan"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "example": "123"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "Updated Name"}, "type": {"type": "string", "example": "retainer"}, "duration_years": {"type": "string", "example": "null"}}}}}}, "responses": {"200": {"description": "Plan updated successfully"}, "400": {"description": "Validation error"}, "404": {"description": "Plan not found"}}}, "delete": {"summary": "Delete Plan", "tags": ["Admin Plan"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "example": "123"}}], "responses": {"200": {"description": "Plan deleted successfully"}, "404": {"description": "Plan not found"}}}}}}