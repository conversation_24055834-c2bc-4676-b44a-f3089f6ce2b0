import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable("refinements_aligner", (table) => {
    table.increments("id").primary();
    table
      .integer("patient_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("patients")
      .onDelete("CASCADE");
    table.text("refinement_details").nullable();
    table.string("upper_impression").nullable();
    table.string("lower_impression").nullable();
    // imaging fields
    table.string("profileRepose").nullable();
    table.string("buccalRight").nullable();
    table.string("buccalLeft").nullable();
    table.string("frontalRepose").nullable();
    table.string("frontalSmiling").nullable();
    table.string("labialAnterior").nullable();
    table.string("occlussalLower").nullable();
    table.string("occlussalUpper").nullable();
    table.string("radioGraph1").nullable();
    table.string("radioGraph2").nullable();
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTableIfExists("refinements_aligner");
}