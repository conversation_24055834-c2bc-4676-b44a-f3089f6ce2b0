import { Server, Socket } from "socket.io";
import db from "../../config/db";
import { TABLE } from "../../utils/Database/table";
import path from "path";
import fs from "fs";

// Define max file size (50 MB in bytes)
const MAX_FILE_SIZE = 50 * 1024 * 1024;

// Define allowed file types with more comprehensive MIME types for ZIP files
const ALLOWED_FILE_TYPES = [
  'application/zip',
  'application/x-zip',
  'application/x-zip-compressed',
  'application/octet-stream', // Some browsers report ZIP as octet-stream
  'application/pdf',
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml'
];

export const setupChat = (io: Server, socket: Socket) => {
  const sendError = (msg: string) => {
    socket.emit("responseError", msg);
  };

  // Fetch Conversations (patient-specific)
  socket.on("getConversations", async ({ userId, patientId }) => {
    if (!userId || !patientId) return sendError("User ID and Patient ID are required");

    try {
      const conversations = await db
        .select([
          "c.id",
          "c.sender_id",
          "c.receiver_id",
          "c.patient_id",
          "c.message",
          "c.is_read",
          "c.created_at",
          "u.id as user_id",
          "u.first_name",
          "u.last_name",
          db.raw(`
            CASE
              WHEN u.profile_image IS NOT NULL
              THEN CONCAT('${process.env.BASE_URL}', u.profile_image)
              ELSE NULL
            END as profile_image
          `),
          db.raw(
            `(SELECT COUNT(*)
                FROM ${TABLE.CHATS} as sub
                WHERE 
                  sub.is_read = false AND
                  sub.patient_id = ? AND
                  (
                    (LEAST(sub.sender_id, sub.receiver_id) = LEAST(c.sender_id, c.receiver_id)) AND
                    (GREATEST(sub.sender_id, sub.receiver_id) = GREATEST(c.sender_id, c.receiver_id))
                  ) AND
                  sub.receiver_id = ?
              ) as unread_count
            `,
            [patientId, userId]
          ),
        ])
        .from(function (this: any) {
          this
            .select('*')
            .from(TABLE.CHATS)
            .where('patient_id', patientId)
            .andWhere(function (this: any) {
              this.where('sender_id', userId)
                .orWhere('receiver_id', userId);
            })
            .as('c');
        })
        .join({ u: TABLE.USERS }, function () {
          this.on(function () {
            this.on("c.sender_id", db.raw("?", [userId])).andOn(
              "u.id",
              "=",
              "c.receiver_id"
            );
          }).orOn(function () {
            this.on("c.receiver_id", db.raw("?", [userId])).andOn(
              "u.id",
              "=",
              "c.sender_id"
            );
          });
        })
        .distinctOn([
          db.raw("LEAST(c.sender_id, c.receiver_id)") as any,
          db.raw("GREATEST(c.sender_id, c.receiver_id)") as any,
        ])
        .orderByRaw(`
          LEAST(c.sender_id, c.receiver_id),
          GREATEST(c.sender_id, c.receiver_id),
          c.created_at DESC
        `);

      const formatted = conversations.map((conv: any) => ({
        message_id: conv.id,
        message: conv.message,
        created_at: conv.created_at,
        unread_count: conv.unread_count,
        user: {
          id: conv.user_id,
          name: `${conv.first_name} ${conv.last_name}`,
          profile_image: conv.profile_image,
        },
      }));

      socket.emit("conversationsList", formatted);
    } catch (error) {
      console.error("Fetch conversation error:", error);
      sendError("Failed to fetch conversations");
    }
  });

  // Fetch Messages (patient-specific)
  socket.on("getConversationMessages", async ({ user_id, target_user_id, patient_id }) => {
    try {
      const messages = await db(`${TABLE.CHATS} as c`)
        .select(
          "c.id",
          "c.sender_id",
          "c.receiver_id",
          "c.patient_id",
          "c.message",
          "c.file_url",
          "c.file_name",
          "c.file_type",
          "c.file_size",
          "c.is_read",
          "c.created_at",
          "u.first_name",
          "u.last_name"
        )
        .leftJoin(`${TABLE.USERS} as u`, "c.sender_id", "u.id")
        .where("c.patient_id", patient_id)
        .andWhere(function () {
          this.where("c.sender_id", user_id)
            .andWhere("c.receiver_id", target_user_id)
            .orWhere("c.sender_id", target_user_id)
            .andWhere("c.receiver_id", user_id);
        })
        .orderBy("c.created_at", "asc");

      socket.emit("messagesList", messages);
    } catch (error) {
      console.error("Fetch messages error:", error);
      sendError("Failed to fetch conversation messages");
    }
  });

  // Send Message (with file handling)
  socket.on(
    "sendMessage",
    async (data: {
      sender_id: number;
      receiver_id: number;
      patient_id: number;
      message?: string;
      file_base64?: string;
      file_name?: string;
      file_type?: string;
      file_size?: number;
    }) => {
      try {
        const {
          sender_id,
          receiver_id,
          patient_id,
          message = "",
          file_base64,
          file_name,
          file_type,
          file_size,
        } = data;

        // Validate file size
        if (file_size && file_size > MAX_FILE_SIZE) {
          return sendError("File size exceeds the 50 MB limit");
        }

        // Validate file type with special handling for ZIP files
        if (file_type && file_name) {
          const isAllowedMimeType = ALLOWED_FILE_TYPES.includes(file_type);
          const fileExtension = file_name.split('.').pop()?.toLowerCase();
          
          // Allow if MIME type is in allowed list OR if it's octet-stream with .zip extension
          const isAllowed = isAllowedMimeType || 
            (file_type === 'application/octet-stream' && fileExtension === 'zip');
          
          if (!isAllowed) {
            return sendError(`File type not allowed (${file_type}). Please upload a ZIP, PDF, or image file.`);
          }
        }

        let fileUrl: string | null = null;

        // Handle Base64 file payload
        if (file_base64 && file_name && file_type) {
          // Additional validation for base64 size
          const raw = file_base64.replace(/^data:[^;]+;base64,/, "");
          const actualSize = Math.ceil((raw.length * 3) / 4);
          
          if (actualSize > MAX_FILE_SIZE) {
            return sendError("File size exceeds the 50 MB limit");
          }
          
          // Additional validation for file type using the MIME type in base64
          const mimeMatch = file_base64.match(/^data:([^;]+);base64,/);
          if (mimeMatch) {
            const mimeType = mimeMatch[1];
            const fileExtension = file_name.split('.').pop()?.toLowerCase();
            
            // Special handling for ZIP files which might have different MIME types
            const isAllowed = ALLOWED_FILE_TYPES.includes(mimeType) || 
              (mimeType === 'application/octet-stream' && fileExtension === 'zip');
              
            if (!isAllowed) {
              return sendError(`File type not allowed based on content analysis (${mimeType})`);
            }
          }
          
          // For ZIP files that might not have the data:mime;base64 prefix
          // Some browsers/libraries might send ZIP files without proper MIME prefix
          if (!file_base64.startsWith('data:')) {
            const fileExtension = file_name.split('.').pop()?.toLowerCase();
            if (fileExtension === 'zip') {
              // Assume it's a raw base64 string for ZIP
              const buffer = Buffer.from(file_base64, "base64");
              
              const uploadDir = path.resolve(__dirname, "../../../public/chat");
              if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
              }
              
              const filename = `${Date.now()}_${file_name}`;
              const fullPath = path.join(uploadDir, filename);
              fs.writeFileSync(fullPath, buffer);
              console.log("ZIP file saved at:", fullPath);
              
              fileUrl = `/chat/${filename}`;
            } else {
              return sendError("Invalid file format");
            }
          } else {
            // Normal path for files with proper data:mime;base64 prefix
            const buffer = Buffer.from(raw, "base64");
            
            const uploadDir = path.resolve(__dirname, "../../../public/chat");
            if (!fs.existsSync(uploadDir)) {
              fs.mkdirSync(uploadDir, { recursive: true });
            }
            
            const filename = `${Date.now()}_${file_name}`;
            const fullPath = path.join(uploadDir, filename);
            fs.writeFileSync(fullPath, buffer);
            console.log("File saved at:", fullPath);
            
            fileUrl = `/chat/${filename}`;
          }
        }

        // Insert into DB
        const [saved] = await db(TABLE.CHATS)
          .insert({
            sender_id,
            receiver_id,
            patient_id,
            message: message || null,
            file_url: fileUrl,
            file_name,
            file_type,
            file_size,
          })
          .returning("*");

        // Emit to both sender and receiver
        socket.emit("newMessage", saved);
        io.to(`user_${receiver_id}`).emit("newMessage", saved);
      } catch (err) {
        console.error("Send message error:", err);
        sendError("Failed to send message");
      }
    }
  );

  // Mark Messages as Read
  socket.on("markMessagesAsRead", async ({ user_id, sender_id, patient_id }) => {
    try {
      await db(TABLE.CHATS)
        .where("sender_id", sender_id)
        .andWhere("receiver_id", user_id)
        .andWhere("patient_id", patient_id)
        .andWhere("is_read", false)
        .update({ is_read: true });

      io.to(`user_${sender_id}`).emit("messagesMarkedAsRead", {
        readerId: user_id,
        senderId: sender_id,
        patientId: patient_id,
      });
    } catch (error) {
      console.error("Mark as read error:", error);
      sendError("Failed to mark messages as read");
    }
  });

  // Delete Message
  socket.on("deleteMessage", async ({ messageId, sender_id, receiver_id, patient_id }) => {
    try {
      const deleted = await db(TABLE.CHATS)
        .where({ id: messageId, sender_id, patient_id })
        .del();

      if (!deleted) return sendError("Message not found or unauthorized");

      socket.emit("messageDeleted", { messageId });
      io.to(`user_${receiver_id}`).emit("messageDeleted", { messageId });
    } catch (error) {
      console.error("Delete message error:", error);
      sendError("Failed to delete message");
    }
  });
};
