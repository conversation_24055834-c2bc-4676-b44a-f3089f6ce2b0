import type { K<PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TABLE.DOCTOR_EMPLOYEES, (table) => {
    table.increments("id").primary();
    table.integer("doctor_id").unsigned().notNullable();
    table.integer("employee_id").unsigned().notNullable();
    table.foreign("doctor_id").references("id").inTable(TABLE.USERS);
    table.foreign("employee_id").references("id").inTable(TABLE.USERS);
    table.unique(["doctor_id", "employee_id"]);
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TABLE.DOCTOR_EMPLOYEES);
}