{"name": "orthodontic-backend", "version": "1.0.0", "description": "Backend for Orthodontic project", "main": "src/index.ts", "dependencies": {"@types/handlebars": "^4.0.40", "axios": "1.8.1", "bcrypt": "5.1.1", "bcryptjs": "3.0.2", "cors": "2.8.5", "dayjs": "1.11.13", "deepmerge": "^4.3.1", "dotenv": "16.4.7", "express": "4.21.2", "express-fileupload": "1.5.1", "express-rate-limit": "7.5.0", "file-type": "^21.0.0", "firebase-admin": "^13.4.0", "handlebars": "^4.7.8", "jsonwebtoken": "9.0.2", "knex": "^3.1.0", "localtunnel": "^1.8.3", "morgan": "1.10.0", "multer": "1.4.5-lts.1", "nodemailer": "6.10.0", "paypal-rest-sdk": "1.8.1", "pg": "8.13.3", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui": "^3.29.0", "swagger-ui-express": "^5.0.1", "zod": "3.24.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/express-fileupload": "^1.5.1", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.13.9", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.11", "@types/swagger-ui-express": "^4.1.8", "concurrently": "^8.2.2", "cross-port-killer": "^1.4.0", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.2", "undici-types": "^7.4.0"}, "scripts": {"dev": "concurrently \"nodemon src/index.ts\" \"docker-compose up -d\"", "start": "ts-node src/index.ts", "prestart": "npx knex migrate:latest", "build": "tsc", "knex": "npx knex --knexfile knexfile.ts", "migration": "bash src/scripts/run_migrations.sh", "seed": "npx knex seed:run"}, "keywords": [], "author": "", "license": "ISC"}