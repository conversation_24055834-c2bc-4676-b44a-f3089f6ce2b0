import type { <PERSON><PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TABLE.PERMISSIONS, (table) => {
    table.increments("id").primary();
    table.integer("role_id").unsigned().notNullable();
    table.integer("module_id").unsigned().notNullable();
    
    // CRUD operation columns
    table.boolean("create").defaultTo(0).notNullable();
    table.boolean("list").defaultTo(0).notNullable();
    table.boolean("view").defaultTo(0).notNullable();
    table.boolean("edit").defaultTo(0).notNullable();
    table.boolean("delete").defaultTo(0).notNullable();
    
    // Foreign keys
    table.foreign("role_id").references("id").inTable(TABLE.ROLES);
    table.foreign("module_id").references("id").inTable(TABLE.MODULES);
    
    // Unique constraint
    table.unique(["role_id", "module_id"]);
    
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TABLE.PERMISSIONS);
}
