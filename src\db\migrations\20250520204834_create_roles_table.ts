import type { <PERSON><PERSON> } from "knex";
import { UserRole } from "../../utils/enums/users.enum";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("roles", (table) => {
    table.increments("id").primary();
    table.string("role_name").notNullable().unique();
    table.timestamps(true, true);
  });
  await knex("roles").insert([
    { role_name: UserRole.SUPERADMIN },
    { role_name: UserRole.ADMIN },
    { role_name: UserRole.DOCTOR },
    { role_name: UserRole.EMPLOYEE },
  ]);

  await knex.schema.alterTable(TABLE.USERS, (table) => {
    // Add the new role_id column
    table.integer("role_id").unsigned().nullable();
    table
      .foreign("role_id")
      .references("id")
      .inTable("roles")
      .onDelete("SET NULL");
  });
  const users = await knex(TABLE.USERS).select("id", "role");

  for (const user of users) {
    if (user.role) {
      const role = await knex("roles").where("role_name", user.role).first();
      if (role) {
        await knex(TABLE.USERS)
          .where("id", user.id)
          .update({ role_id: role.id });
      }
    }
  }

  // Step 5: Drop the old role column
  return knex.schema.alterTable(TABLE.USERS, (table) => {
    table.dropColumn("role");
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE.USERS, (table) => {
    table.string("role").nullable();
  });
  const users = await knex(TABLE.USERS).select("id", "role_id");

  for (const user of users) {
    if (user.role_id) {
      const role = await knex("roles").where("id", user.role_id).first();
      if (role) {
        await knex(TABLE.USERS)
          .where("id", user.id)
          .update({ role: role.role_name });
      }
    }
  }
  await knex.schema.alterTable(TABLE.USERS, (table) => {
    table.dropForeign(["role_id"]);
    table.dropColumn("role_id");
  });
  return knex.schema.dropTable("roles");
}
