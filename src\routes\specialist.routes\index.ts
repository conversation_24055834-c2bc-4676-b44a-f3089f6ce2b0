import { Router } from "express";

import { storageData } from "../../utils/services/multer";
import {
  // reviewPatientFile,
  reviewPatientVersion,
} from "../../controller/specialist.controller";
import { authMiddleware } from "../../middlewares/authMiddleware";

const router = Router();
const upload = storageData("specialist");

// router.post(
//   "/patient-files/:patientFileId/review",
//   upload.single("rejection_pdf"),
//   reviewPatientFile
// );
router.post(
  "/patient-versions/:versionId/review",
  authMiddleware,
  upload.none(),
  reviewPatientVersion
);
export default router;
