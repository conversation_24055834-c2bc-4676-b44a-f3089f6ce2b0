import { Request, Response } from "express";
import db from "../../config/db";
import { TABLE } from "../../utils/Database/table";
import { UserRole } from "../../utils/enums/users.enum";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";
import asyncHandler from "../../middlewares/trycatch";

// Get all modules and permissions
export const getModulesAndPermissions = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const modules = await db(TABLE.MODULES).select("*");

      const modulesWithPermissions = await Promise.all(
        modules.map(async (module) => {
          const permissions = await db(TABLE.PERMISSIONS)
            .where("module_id", module.id)
            .select("*");

          return {
            ...module,
            permissions,
          };
        })
      );

      sendResponse(
        res,
        200,
        "Modules and permissions retrieved successfully",
        true,
        modulesWithPermissions
      );
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
// Assign permissions to an employee
// export const assignPermissions = asyncHandler(async (req: Request, res: Response) => {
//   try {
//     const { employeeId, permissions } = req.body;

//     if (!req.user || req.user.role !== UserRole.DOCTOR) {
//       sendResponse(res, 403, "Only doctors can assign permissions", false);
//       return;
//     }

//     // Verify this employee belongs to the doctor
//     const relationship = await db(TABLE.DOCTOR_EMPLOYEES)
//       .where({
//         doctor_id: req.user.id,
//         employee_id: employeeId
//       })
//       .first();

//     if (!relationship) {
//       sendResponse(res, 403, "You can only manage permissions for your own employees", false);
//       return;
//     }

//     // Get employee with role
//     const employee = await db(TABLE.USERS)
//       .where('id', employeeId)
//       .select('id', 'role_id', 'first_name', 'last_name', 'email')
//       .first();

//     if (!employee || !employee.role_id) {
//       sendResponse(res, 404, "Employee not found or has no assigned role", false);
//       return;
//     }

//     // Begin transaction
//     await db.transaction(async (trx) => {
//       // Remove existing permissions
//       await trx(TABLE.PERMISSIONS)
//         .where('role_id', employee.role_id)
//         .del();

//       // Add new permissions
//       if (permissions && permissions.length > 0) {
//         // Group permissions by module
//         const modulePermissions: Record<string, Record<string, number>> = {};

//         // Process permissions array
//         permissions.forEach((perm: string) => {
//           const [moduleId, action] = perm.split('_');

//           if (!modulePermissions[moduleId]) {
//             modulePermissions[moduleId] = {
//               create: 0,
//               list: 0,
//               view: 0,
//               edit: 0,
//               delete: 0
//             };
//           }

//           if (['create', 'list', 'view', 'edit', 'delete'].includes(action)) {
//             modulePermissions[moduleId][action] = 1;
//           }
//         });

//         // Insert permissions for each module
//         const permissionRecords = Object.entries(modulePermissions).map(([moduleId, actions]) => ({
//           role_id: employee.role_id,
//           module_id: parseInt(moduleId),
//           create: actions.create,
//           list: actions.list,
//           view: actions.view,
//           edit: actions.edit,
//           delete: actions.delete
//         }));

//         if (permissionRecords.length > 0) {
//           await trx(TABLE.PERMISSIONS).insert(permissionRecords);
//         }
//       }
//     });

//     sendResponse(res, 200, "Permissions assigned successfully", true);
//   } catch (error: any) {
//     console.error(error);
//     sendResponse(res, 500, error.message, false);
//   }
// });

// Get employee permissions
export const getEmployeePermissions = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { employeeId } = req.params;

      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        sendResponse(
          res,
          403,
          "Only doctors can view employee permissions",
          false
        );
        return;
      }

      // Verify this employee belongs to the doctor
      const relationship = await db(TABLE.DOCTOR_EMPLOYEES)
        .where({
          doctor_id: req.user.id,
          employee_id: employeeId,
        })
        .first();

      if (!relationship) {
        sendResponse(
          res,
          403,
          "You can only view permissions for your own employees",
          false
        );
        return;
      }

      // Get employee with role
      const employee = await db(TABLE.USERS)
        .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
        .where(`${TABLE.USERS}.id`, employeeId)
        .select(
          `${TABLE.USERS}.id`,
          `${TABLE.USERS}.first_name`,
          `${TABLE.USERS}.last_name`,
          `${TABLE.USERS}.email`,
          `${TABLE.ROLES}.id as role_id`,
          `${TABLE.ROLES}.role_name as role`
        )
        .first();

      if (!employee) {
        sendResponse(res, 404, "Employee not found", false);
        return;
      }

      // Get permissions for this employee's role
      const permissions = await db(TABLE.PERMISSIONS)
        .join(
          TABLE.MODULES,
          `${TABLE.PERMISSIONS}.module_id`,
          "=",
          `${TABLE.MODULES}.id`
        )
        .where(`${TABLE.PERMISSIONS}.role_id`, employee.role_id)
        .select([
          `${TABLE.PERMISSIONS}.*`,
          `${TABLE.MODULES}.name as module_name`,
          `${TABLE.MODULES}.description as module_description`,
        ]);

      // Format permissions for response
      const formattedPermissions = permissions.map((p) => ({
        moduleId: p.module_id,
        moduleName: p.module_name,
        moduleDescription: p.module_description,
        permissions: {
          create: Boolean(p.create),
          list: Boolean(p.list),
          view: Boolean(p.view),
          edit: Boolean(p.edit),
          delete: Boolean(p.delete),
        },
      }));

      const result = {
        employee: {
          id: employee.id,
          name: `${employee.first_name} ${employee.last_name}`,
          email: employee.email,
          role: employee.role,
        },
        permissions: formattedPermissions,
      };

      sendResponse(
        res,
        200,
        "Employee permissions retrieved successfully",
        true,
        result
      );
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
// Get permissions for the currently logged-in user
// export const getCurrentUserPermissions = asyncHandler(async (req: Request, res: Response) => {
//   try {
//     if (!req.user || !req.user.id) {
//       sendResponse(res, 401, "Unauthorized! Please login first.", false);
//       return;
//     }

//     // Get user with role
//     const user = await db(TABLE.USERS)
//       .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, '=', `${TABLE.ROLES}.id`)
//       .where(`${TABLE.USERS}.id`, req.user.id)
//       .select([
//         `${TABLE.USERS}.id`,
//         `${TABLE.USERS}.first_name`,
//         `${TABLE.USERS}.last_name`,
//         `${TABLE.USERS}.email`,
//         `${TABLE.ROLES}.id as role_id`,
//         `${TABLE.ROLES}.role_name as role`
//       ])
//       .first();

//     if (!user) {
//       sendResponse(res, 404, "User not found", false);
//       return;
//     }

//     // Get permissions for this user's role
//     const permissions = await db(TABLE.PERMISSIONS)
//       .join(TABLE.MODULES, `${TABLE.PERMISSIONS}.module_id`, '=', `${TABLE.MODULES}.id`)
//       .where(`${TABLE.PERMISSIONS}.role_id`, user.role_id)
//       .select([
//         `${TABLE.PERMISSIONS}.*`,
//         `${TABLE.MODULES}.id as module_id`,
//         `${TABLE.MODULES}.name as module_name`,
//         `${TABLE.MODULES}.description as module_description`
//       ]);

//     // Group permissions by module
//     const moduleMap = new Map();

//     permissions.forEach(permission => {
//       if (!moduleMap.has(permission.module_id)) {
//         moduleMap.set(permission.module_id, {
//           id: permission.module_id,
//           name: permission.module_name,
//           description: permission.module_description,
//           permissions: {
//             create: Boolean(permission.create),
//             list: Boolean(permission.list),
//             view: Boolean(permission.view),
//             edit: Boolean(permission.edit),
//             delete: Boolean(permission.delete)
//           }
//         });
//       }
//     });

//     const result = {
//       user: {
//         id: user.id,
//         name: `${user.first_name} ${user.last_name}`,
//         email: user.email,
//         role: user.role
//       },
//       modules: Array.from(moduleMap.values())
//     };

//     sendResponse(res, 200, "User permissions retrieved successfully", true, result);
//   } catch (error: any) {
//     console.error(error);
//     sendResponse(res, 500, error.message, false);
//   }
// });

// Assign permissions to a role
export const assignRolePermissions = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { roleId } = req.body;
      const raw = req.body.modulePermissions; // raw JSON string

      // Validate presence
      if (!roleId || !raw) {
        return sendResponse(
          res,
          400,
          "roleId and modulePermissions are required",
          false
        );
      }

      // Parse JSON out of the form-field
      let modulePermissions;
      try {
        modulePermissions = JSON.parse(raw);
      } catch {
        return sendResponse(
          res,
          400,
          "modulePermissions must be valid JSON",
          false
        );
      }

      // Ensure it's an array
      if (!Array.isArray(modulePermissions)) {
        return sendResponse(
          res,
          400,
          "modulePermissions must be an array",
          false
        );
      }

      // Check admin privileges
      if (
        !req.user ||
        (req.user.role !== UserRole.ADMIN &&
          req.user.role !== UserRole.SUPERADMIN)
      ) {
        return sendResponse(
          res,
          403,
          "Only administrators can assign role permissions",
          false
        );
      }

      // Check if role exists
      const role = await db(TABLE.ROLES).where("id", roleId).first();
      if (!role) {
        return sendResponse(res, 404, "Role not found", false);
      }

      // Prevent modifying SUPERADMIN role
      if (role.role_name === UserRole.SUPERADMIN) {
        return sendResponse(
          res,
          403,
          "Cannot modify permissions for the super admin role",
          false
        );
      }

      // Begin transaction
      await db.transaction(async (trx) => {
        // Remove existing permissions for this role
        await trx(TABLE.PERMISSIONS).where("role_id", roleId).del();

        // Add new permissions if any
        if (modulePermissions.length > 0) {
          const moduleIds = modulePermissions.map((p: any) => p.moduleId);
          const existingModules = await trx(TABLE.MODULES)
            .whereIn("id", moduleIds)
            .select("id");
          const validModuleIds = existingModules.map((m) => m.id);

          const validModulePermissions = modulePermissions.filter((p: any) =>
            validModuleIds.includes(Number(p.moduleId))
          );
          if (validModulePermissions.length !== modulePermissions.length) {
            console.warn(
              "Some module IDs were invalid and have been filtered out"
            );
          }

          if (validModulePermissions.length > 0) {
            const permissionRecords = validModulePermissions.map((p: any) => ({
              role_id: roleId,
              module_id: p.moduleId,
              create: p.permissions.create ? 1 : 0,
              list: p.permissions.list ? 1 : 0,
              view: p.permissions.view ? 1 : 0,
              edit: p.permissions.edit ? 1 : 0,
              delete: p.permissions.delete ? 1 : 0,
            }));
            await trx(TABLE.PERMISSIONS).insert(permissionRecords);
          }
        }
      });

      sendResponse(
        res,
        200,
        `Permissions assigned successfully to ${role.role_name} role`,
        true
      );
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
// Get permissions for a role
export const getRolePermissions = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { roleId } = req.params;

      // Check if role exists
      const role = await db(TABLE.ROLES).where("id", roleId).first();

      if (!role) {
        sendResponse(res, 404, "Role not found", false);
        return;
      }

      // Get permissions for this role
      const permissions = await db(TABLE.PERMISSIONS)
        .join(
          TABLE.MODULES,
          `${TABLE.PERMISSIONS}.module_id`,
          "=",
          `${TABLE.MODULES}.id`
        )
        .where(`${TABLE.PERMISSIONS}.role_id`, roleId)
        .select([
          `${TABLE.PERMISSIONS}.*`,
          `${TABLE.MODULES}.name as module_name`,
          `${TABLE.MODULES}.description as module_description`,
        ]);

      const result = {
        role: {
          id: role.id,
          name: role.role_name,
        },
        permissions: permissions.map((p) => ({
          id: p.id,
          moduleId: p.module_id,
          moduleName: p.module_name,
          moduleDescription: p.module_description,
          permissions: {
            create: Boolean(p.create),
            list: Boolean(p.list),
            view: Boolean(p.view),
            edit: Boolean(p.edit),
            delete: Boolean(p.delete),
          },
        })),
      };

      sendResponse(
        res,
        200,
        "Role permissions retrieved successfully",
        true,
        result
      );
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
// Get current user's permissions
export const getCurrentUserPermissions = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      if (!req.user) {
        sendResponse(res, 401, "Unauthorized! Please login first.", false);
        return;
      }

      // Get user with role
      const user = await db(TABLE.USERS)
        .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
        .where(`${TABLE.USERS}.id`, req.user.id)
        .select([
          `${TABLE.USERS}.id`,
          `${TABLE.USERS}.first_name`,
          `${TABLE.USERS}.last_name`,
          `${TABLE.USERS}.email`,
          `${TABLE.ROLES}.id as role_id`,
          `${TABLE.ROLES}.role_name as role`,
        ])
        .first();

      if (!user) {
        sendResponse(res, 404, "User not found", false);
        return;
      }

      // Get permissions for this user's role
      const permissions = await db(TABLE.PERMISSIONS)
        .join(
          TABLE.MODULES,
          `${TABLE.PERMISSIONS}.module_id`,
          "=",
          `${TABLE.MODULES}.id`
        )
        .where(`${TABLE.PERMISSIONS}.role_id`, user.role_id)
        .select([
          `${TABLE.PERMISSIONS}.*`,
          `${TABLE.MODULES}.id as module_id`,
          `${TABLE.MODULES}.name as module_name`,
          `${TABLE.MODULES}.description as module_description`,
        ]);

      // Group permissions by module
      const moduleMap = new Map();

      permissions.forEach((permission) => {
        if (!moduleMap.has(permission.module_id)) {
          moduleMap.set(permission.module_id, {
            id: permission.module_id,
            name: permission.module_name,
            description: permission.module_description,
            permissions: {
              create: Boolean(permission.create),
              list: Boolean(permission.list),
              view: Boolean(permission.view),
              edit: Boolean(permission.edit),
              delete: Boolean(permission.delete),
            },
          });
        }
      });

      const result = {
        user: {
          id: user.id,
          name: `${user.first_name} ${user.last_name}`,
          email: user.email,
          role: user.role,
        },
        modules: Array.from(moduleMap.values()),
      };

      sendResponse(
        res,
        200,
        "User permissions retrieved successfully",
        true,
        result
      );
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const getAllModules = asyncHandler(async (req: Request, res: Response) => {
  try {
    // Fetch all modules from database
    const modules = await db(TABLE.MODULES).select('*').orderBy('name', 'asc');
    
    sendResponse(
      res, 
      200, 
      "Modules retrieved successfully", 
      true, 
      modules
    );
  } catch (error: any) {
    console.error(error);
    sendResponse(
      res, 
      500, 
      error.message || "Failed to retrieve modules", 
      false
    );
  }
});