import { K<PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";
// import { TABLE } from "../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TABLE.DOCTOR_ADDRESSES, (table) => {
    table.increments("id").primary();
    table.integer("doctor_id").unsigned().notNullable();
    table.string("clinic_name").notNullable();
    table.string("street_address").notNullable();
    table.string("city").notNullable();
    table.string("postal_code");
    table.string("phone_number");
    table.timestamps(true, true);

    // Foreign key constraint
    table
      .foreign("doctor_id")
      .references("id")
      .inTable(TABLE.USERS)
      .onDelete("CASCADE");
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable(TABLE.DOCTOR_ADDRESSES);
}