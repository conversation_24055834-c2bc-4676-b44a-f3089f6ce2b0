import nodemailer from "nodemailer";
import dotenv from "dotenv";

dotenv.config();

// Create a reusable transporter object
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || "smtp.gmail.com",
  port: parseInt(process.env.EMAIL_PORT || "587"),
  secure: process.env.EMAIL_SECURE === "true",
  auth: {
    user: process.env.EMAIL_USER || "<EMAIL>",
    pass: process.env.EMAIL_PASSWORD || "hzkvvyyytvarnuhy",
  },
});

interface EmployeeCredentials {
  email: string;
  password: string;
  name: string;
  doctorName: string;
}

export const sendEmployeeCredentialEmail = async (employeeData: EmployeeCredentials) => {
  const { email, password, name, doctorName } = employeeData;
  
  const mailOptions = {
    from: process.env.EMAIL_FROM || "<EMAIL>",
    to: email,
    subject: "Your Employee Account Credentials",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2 style="color: #333;">Welcome to Orthodontic Lab</h2>
        <p>Hello ${name},</p>
        <p>Your employee account has been created by Dr. ${doctorName}. Below are your login credentials:</p>
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Password:</strong> ${password}</p>
        </div>
        <p>Please log in using these credentials and change your password immediately for security purposes.</p>
        <p>If you have any questions, please contact your doctor or administrator.</p>
        <p>Best regards,<br>Orthodontic Lab Team</p>
      </div>
    `,
  };

  await transporter.sendMail(mailOptions);
};
