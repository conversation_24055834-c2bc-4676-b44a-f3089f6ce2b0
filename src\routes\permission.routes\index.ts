import { Router } from "express";
import { authMiddleware } from "../../middlewares/authMiddleware";
import {
  getModulesAndPermissions,
  getEmployeePermissions,
  getCurrentUserPermissions,
  assignRolePermissions,
  getRolePermissions,
  getAllModules,
} from "../../controller/permission.controller";
import { authAdminMiddleware } from "../../middlewares/authAdminMiddleware";
import { storageData } from "../../utils/services/multer";

const router = Router();
const upload = storageData("permissions");

// Get all modules and permissions (admin only)
router.get("/modules", authMiddleware, getModulesAndPermissions);
// Get employee permissions (doctor only)
router.get("/employee/:employeeId", authMiddleware, getEmployeePermissions);
// Get current user's permissions
router.get("/me", authMiddleware, getCurrentUserPermissions);
// Get permissions for a specific role
router.get("/role/:roleId", authMiddleware, getRolePermissions);
// Assign permissions to a role (admin only)
router.post("/role", authAdminMiddleware, upload.none(), assignRolePermissions);

router.get("/all/modules", upload.none(), getAllModules);
export default router;
