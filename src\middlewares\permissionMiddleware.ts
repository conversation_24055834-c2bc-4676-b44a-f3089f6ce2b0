import { Request, Response, NextFunction } from "express";
import { sendResponse } from "../utils/helperFunctions/responseHelper";
import db from "../config/db";
import { TABLE } from "../utils/Database/table";

/**
 * Middleware to check if a user has permission to perform an action
 * @param moduleName Name of the module
 * @param action Permission action (create, list, view, edit, delete)
 */
export const checkPermission = (moduleName: string, action: 'create' | 'list' | 'view' | 'edit' | 'delete') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user || !req.user.id) {
        sendResponse(res, 401, "Unauthorized! Please login first.", false);
        return;
      }

      // Get user's role_id
      const user = await db(TABLE.USERS)
        .where('id', req.user.id)
        .select('role_id')
        .first();

      if (!user || !user.role_id) {
        sendResponse(res, 403, "User has no assigned role", false);
        return;
      }

      // Get module ID
      const module = await db(TABLE.MODULES)
        .where('name', moduleName)
        .select('id')
        .first();

      if (!module) {
        sendResponse(res, 404, "Module not found", false);
        return;
      }

      // Check if user has permission
      const permission = await db(TABLE.PERMISSIONS)
        .where({
          role_id: user.role_id,
          module_id: module.id
        })
        .select(action)
        .first();

      if (!permission || !permission[action]) {
        sendResponse(res, 403, "You don't have permission to perform this action", false);
        return;
      }

      // User has permission, proceed to the next middleware
      next();
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  };
};
