{"paths": {"/auth/register-user": {"post": {"summary": "Create a new user (admin only)", "tags": ["User Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe", "description": "Optional. Must be unique if provided."}, "role_id": {"type": "string", "example": "2", "description": "Role ID from roles table."}}, "required": ["first_name", "last_name", "email", "role_id"]}}}}, "responses": {"201": {"description": "User created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User created successfully"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "username": {"type": ["string", "null"], "example": "johndoe"}, "role_id": {"type": "integer", "example": 2}, "role": {"type": "string", "example": "DOCTOR"}, "is_active": {"type": "boolean", "example": true}, "is_verified": {"type": "boolean", "example": true}}}}}}}}, "400": {"description": "Validation error, email or username already exists"}, "403": {"description": "Only admins can create new users"}, "500": {"description": "Internal server error"}}}}, "/auth/login-user": {"post": {"summary": "Login user (email or username)", "tags": ["User Authentication"], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe123"}, "password": {"type": "string", "example": "securePassword123"}}, "required": ["password"]}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Login successful"}, "data": {"type": "object", "properties": {"accessToken": {"type": "string", "example": "jwt_token_here"}, "user": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe123"}, "role": {"type": "string", "example": "staff"}, "profile_image": {"type": "string", "example": "http://localhost:5000/images/profile.jpg"}}}}}}}}}}, "400": {"description": "Validation error or Invalid credentials", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Invalid email/username or password"}}}}}}, "403": {"description": "Unauthorized access or inactive account", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "You are not authorized to perform this action"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}}}}}}}}}, "/auth/check-email": {"get": {"summary": "Check if email already exists", "tags": ["User Authentication"], "parameters": [{"name": "email", "in": "query", "required": true, "description": "The email address to check for uniqueness", "schema": {"type": "string", "format": "email"}}], "responses": {"200": {"description": "Email is unique", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Email is unique."}, "success": {"type": "boolean", "example": true}}}}}}, "404": {"description": "Email already exists", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Email already exists."}, "success": {"type": "boolean", "example": false}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Internal server error"}, "success": {"type": "boolean", "example": false}}}}}}}}}, "/auth/forget-password": {"post": {"summary": "Reset password and send new credentials via email", "tags": ["User Authentication"], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "username": {"type": "string", "example": "john_doe"}}, "oneOf": [{"required": ["email"]}, {"required": ["username"]}]}}}}, "responses": {"200": {"description": "New password generated and sent to registered email", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "New password sent to your registered email"}}}}}}, "400": {"description": "Missing required fields", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Please provide either email or username"}}}}}, "403": {"description": "Account not verified", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Account is not verified"}}}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "User not found with provided credentials"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}, "error": {"type": "object"}}}}}}}}}}, "/auth/reset-password": {"put": {"summary": "Reset password using email (after OTP verification)", "tags": ["User Authentication"], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "NewSecurePassword123!"}, "confirmPassword": {"type": "string", "example": "NewSecurePassword123!"}}, "required": ["email", "password", "confirmPassword"]}}}}, "responses": {"200": {"description": "Your account password has been reset successfully"}, "400": {"description": "Account not found"}, "500": {"description": "Internal server error"}}}}, "/auth/change-password": {"put": {"summary": "Change password for logged-in user", "tags": ["User Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"currentPassword": {"type": "string", "example": "OldPass@123"}, "password": {"type": "string", "example": "NewSecurePass@456"}, "confirmPassword": {"type": "string", "example": "NewSecurePass@456"}}, "required": ["currentPassword", "password"]}}}}, "responses": {"200": {"description": "Password changed successfully"}, "400": {"description": "Account not found or current password is incorrect"}, "401": {"description": "Unauthorized or validation failed"}, "500": {"description": "Internal server error"}}}}, "/auth/delete-account": {"delete": {"summary": "Delete logged-in user's account", "tags": ["User Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "Account deleted successfully"}, "401": {"description": "Unauthorized"}, "404": {"description": "Account not found"}, "500": {"description": "Internal server error"}}}}, "/auth/profile": {"get": {"summary": "Get logged-in user's profile", "tags": ["User Authentication"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User profile fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "role": {"type": "string"}, "phone": {"type": "string"}, "profile_image": {"type": "string", "format": "uri"}, "is_verified": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time"}}}}}}}}, "400": {"description": "User not found"}, "500": {"description": "Internal server error"}}}}, "/auth/update-profile": {"put": {"summary": "Update the logged-in user's profile", "tags": ["User Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"first_name": {"type": "string", "description": "User's first name"}, "last_name": {"type": "string", "description": "User's last name"}, "zip_code": {"type": "string", "description": "User's zip code"}, "profile_image": {"type": "string", "format": "binary", "description": "Profile image file (optional)"}}, "required": ["first_name", "last_name", "zip_code"]}}}}, "responses": {"200": {"description": "Profile updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "role": {"type": "string"}, "zip_code": {"type": "string"}, "profile_image": {"type": "string", "format": "uri"}, "is_verified": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time"}}}}}}}}, "400": {"description": "User account not found"}, "500": {"description": "Internal server error"}}}}, "/auth/check-session": {"get": {"summary": "Check active session and get user data", "tags": ["User Authentication"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Active session found", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Active session found"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "email": {"type": "string", "example": "<EMAIL>"}, "username": {"type": "string", "example": "johndoe"}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "role_id": {"type": "integer", "example": 2}, "is_verified": {"type": "boolean", "example": true}, "profile_image": {"type": "string", "example": "http://localhost:5000/uploads/profile.jpg", "nullable": true}}}}}}}}, "401": {"description": "Session expired or invalid", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Session expired or invalid"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}}}}}}}}}, "/auth/email/change/request": {"post": {"summary": "Request email change with verification token", "tags": ["User Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"newEmail": {"type": "string", "format": "email", "example": "<EMAIL>"}}, "required": ["newEmail"]}}}}, "responses": {"200": {"description": "Verification token sent", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Verification token sent to your current email"}}}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "New email is required"}}}}}}, "404": {"description": "User not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "User not found"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}}}}}}}}}, "/auth/verify-email": {"get": {"summary": "Verify email change token", "tags": ["User Authentication"], "security": [{"bearerAuth": []}], "parameters": [{"name": "token", "in": "query", "required": true, "schema": {"type": "string", "example": "123456"}}], "responses": {"200": {"description": "<PERSON><PERSON> verified successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Token verified. You can now confirm your new email."}, "data": {"type": "object", "properties": {"newEmail": {"type": "string", "example": "<EMAIL>"}}}}}}}}, "400": {"description": "Invalid token", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Invalid or expired token"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}}}}}}}}}, "/auth/email/change/confirm": {"post": {"summary": "Confirm email change after verification", "tags": ["User Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"newEmail": {"type": "string", "format": "email", "example": "<EMAIL>"}}, "required": ["newEmail"]}}}}, "responses": {"200": {"description": "Email updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Email changed successfully"}}}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "No verified email change request found"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Internal server error"}}}}}}}}}}}