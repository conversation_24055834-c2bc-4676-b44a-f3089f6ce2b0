import type { <PERSON><PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";
import bcrypt from "bcryptjs";
import { UserRole } from "../../utils/enums/users.enum";

export async function seed(knex: Knex): Promise<void> {
  try {
    // Get the role ID for SUPERADMIN
    const superadminRole = await knex(TABLE.ROLES)
      .where("role_name", UserRole.SUPERADMIN)
      .first();

    if (!superadminRole) {
      console.error(
        "SUPERADMIN role not found. Please run the roles seed first."
      );
      return;
    }

    // Check if admin already exists
    const existingAdmin = await knex(TABLE.USERS)
      .where("email", "<EMAIL>")
      .first();

    if (existingAdmin) {
      console.log("Admin user already exists, skipping seed");
      return;
    }

    // Create admin user
    const hashedPassword = await bcrypt.hash("Admin@123", 10);

    await knex(TABLE.USERS).insert({
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      username: "superadmin",
      password: hashedPassword,
      role_id: superadminRole.id,
      is_verified: true,
      is_active: true,
    });

    console.log("Superadmin user created successfully");
  } catch (error) {
    console.error("Error seeding superadmin user:", error);
  }
}
