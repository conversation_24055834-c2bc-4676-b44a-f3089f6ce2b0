import { K<PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";


export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable("email_verifications", (table) => {
    table.increments("id").primary();
    table.integer("user_id").unsigned().notNullable().references("id").inTable(TABLE.USERS).onDelete("CASCADE");
    table.string("email").notNullable();
    table.string("token").notNullable().unique();
    table.boolean("is_verified").defaultTo(false);
    table.timestamp("expires_at").notNullable();
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable("email_verifications");
}