// migrations/xxxxx_add_patient_to_chats.ts
import type { Knex } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(TABLE.CHATS, (table) => {
    table
      .integer("patient_id")
      .unsigned()
      .nullable() // allow nulls initially
      .references("id")
      .inTable(TABLE.PATIENTS)
      .onDelete("CASCADE");
  });
}


export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(TABLE.CHATS, (table) => {
    table.dropColumn("patient_id");
  });
}