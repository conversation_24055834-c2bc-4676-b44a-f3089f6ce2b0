import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express";
import { response } from "../../response";
import { User } from "../../types/auth";
import { generateOTP } from "../../helperFunctions/otpGenerator";
import db from "../../../config/db";
import { TABLE } from "../../Database/table";

export const resendVerificationEmail = async (
  user: User,
  res: Response
): Promise<void> => {
  const otp = await generateOTP();

  // Save OTP for verification
  await db(TABLE.PASSWORD_RESETS).insert({
    email: user.email,
    token: otp,
    created_at: new Date(),
  });

  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to: user.email,
    subject: "Your OTP Code for Orthodontic Lab (Valid for 5 Minutes)",
    html: await resendOtpHTMLTemplate(otp, user.is_verified!),
  };

  try {
    await transporter.sendMail(mailOptions);

    if (!user.is_verified) {
      response(
        res,
        201,
        "Your account is not verified, OTP has been sent to your email."
      );
      return;
    } else {
      response(res, 201, "OTP has been sent to your email.");
      return;
    }
  } catch (error) {
    console.error("Error sending email:", error);
    res.status(500).json({
      message:
        "Registration failed. Unable to send OTP. Please try again later.",
    });
    return;
  }
};

export const resendOtpHTMLTemplate = async (otp: string, verified: boolean) => {
  return `<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${
      verified ? "Resend OTP for Verification" : "Complete Your Registration"
    }</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 40px auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .header {
            font-size: 22px;
            font-weight: bold;
            color: #5e9b6d;
        }
        .content {
            margin: 20px 0;
            font-size: 16px;
        }
        .otp {
            display: inline-block;
            font-size: 28px;
            font-weight: bold;
            color: #5e9b6d;
            background-color: #eefaf1;
            padding: 12px 24px;
            border-radius: 6px;
            letter-spacing: 3px;
        }
        .footer {
            margin-top: 30px;
            font-size: 14px;
            color: #777;
        }
        .note {
            font-size: 14px;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            ${
              verified
                ? "Resend OTP for Verification"
                : "Complete Your Registration"
            }
        </div>
        <div class="content">
            <p>Hi,</p>
            <p>We received a request to ${
              verified ? "resend your OTP" : "verify your email"
            } for Orthodontic Lab.</p>
            <p>Use the OTP below to ${
              verified
                ? "log in securely"
                : "complete your account registration"
            }:</p>
            <p class="otp">${otp}</p>
            <p class="note">This OTP is valid for 5 minutes. If you did not request this, please ignore this email or contact support.</p>
        </div>
        <div class="footer">
            <p>Best regards,</p>
            <p><strong>Orthodontic Lab Team</strong></p>
        </div>
    </div>
</body>
</html>`;
};
