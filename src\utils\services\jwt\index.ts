import jwt from "jsonwebtoken";
import { config } from "dotenv";

config();

export const getSignedJwt = (id: string) => {
  return jwt.sign({ id }, process.env.JWT_SECRET! as string, {
    expiresIn: process.env.JWT_EXPIRE as any,
  });
};

export const createResetToken = (id: { email: string }, time: string = "10m") =>
  jwt.sign(id, process.env.JWT_SECRET! as string, {
    expiresIn: time as any,
  });

export const verifyToken = (token: string) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET as string);
  } catch (error) {
    throw new Error("Invalid or expired token");
  }
};
