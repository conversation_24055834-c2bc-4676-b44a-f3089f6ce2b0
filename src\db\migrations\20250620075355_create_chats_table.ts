import type { K<PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";
export async function up(knex: Knex): Promise<void> {
    return knex.schema.createTable(TABLE.CHATS, (table) => {
        table.increments("id").primary();
        table
            .integer("sender_id")
            .unsigned()
            .notNullable()
            .references("id")
            .inTable(TABLE.USERS)
            .onDelete("CASCADE");
        table
            .integer("receiver_id")
            .unsigned()
            .notNullable()
            .references("id")
            .inTable(TABLE.USERS)
            .onDelete("CASCADE");
        table.text("message").nullable()
        table.timestamps(true, true);
    });
}

export async function down(knex: Knex): Promise<void> {
    return knex.schema.dropTable(TABLE.CHATS);
}

