import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable("patient_files", (table) => {
    table.increments("id").primary();
    table.integer("patient_id").unsigned().notNullable();
    table.string("file_name").notNullable();
    table.text("reason").nullable(); // New reason field
    table.timestamps(true, true);

    // Foreign key constraint
    table
      .foreign("patient_id")
      .references("id")
      .inTable("patients")
      .onDelete("CASCADE");
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable("cbct_files");
}