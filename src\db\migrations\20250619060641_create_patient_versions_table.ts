import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("patient_versions", (table) => {
    table.increments("id").primary();

    table
      .integer("patient_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("patients")
      .onDelete("CASCADE");

    table
      .integer("created_by")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("users")
      .onDelete("CASCADE");

    table.integer("version_number").notNullable();
    table.string("title").notNullable();

    table
      .enu("status", [
        "sent_by_doctor",
        "approved_by_specialist",
        "rejected_by_specialist",
        "sent_by_specialist",
        "approved_by_doctor",
        "rejected_by_doctor",
        "accepted",
        "in_working",
        "completed"
      ])
      .notNullable();

    table.text("rejection_reason").nullable();
    table.text("approval_reason").nullable();

    table.integer("upper_steps").nullable();
    table.integer("lower_steps").nullable();

    table.boolean("is_latest_version").defaultTo(true);

    table.jsonb("data").nullable();

    table.timestamp("created_at", { useTz: true }).notNullable().defaultTo(knex.fn.now());
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("patient_versions");
}
