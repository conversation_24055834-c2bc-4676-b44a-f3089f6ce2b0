import { <PERSON><PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";
import { UserRole } from "../../utils/enums/users.enum";

export async function seed(knex: Knex): Promise<void> {
  // Check if roles already exist
  const existingRoles = await knex(TABLE.ROLES).select('role_name');

  if (existingRoles.length > 0) {
    console.log('Roles already exist, skipping seed');
    return;
  }

  // Inserts seed entries
  await knex(TABLE.ROLES).insert([
    { role_name: UserRole.SUPERADMIN },
    { role_name: UserRole.ADMIN },
    { role_name: UserRole.DOCTOR },
    { role_name: UserRole.EMPLOYEE },
    { role_name: UserRole.PATIENT },
    { role_name: UserRole.SPECIALIST }
  ]);

  console.log('Roles seeded successfully');
}
