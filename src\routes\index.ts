import { Router } from "express";
import AUTHROUTES from "./auth.routes";
import DOCTORROUTES from "./doctor.routes";
import PERMISSIONROUTES from "./permission.routes";
import SPECIALISTROUTES from "./specialist.routes";
import ADMINPLANROUTES from "./admin/plan.routes";
const router = Router();

router.use("/auth", AUTHROUTES);
router.use("/admin", ADMINPLANROUTES);
router.use("/doctor", DOCTORROUTES);
router.use("/permissions", PERMISSIONROUTES);
router.use("/specialist", SPECIALISTROUTES);

export default router;
