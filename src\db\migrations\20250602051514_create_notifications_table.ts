import { K<PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(
    TABLE.NOTIFICATIONS,
    (table: Knex.CreateTableBuilder) => {
      table.increments("id").primary();
      table
        .integer("user_id")
        .unsigned()
        .references("id")
        .inTable(TABLE.USERS)
        .onDelete("SET NULL");
      table
        .integer("receiver_id")
        .unsigned()
        .notNullable()
        .references("id")
        .inTable(TABLE.USERS)
        .onDelete("CASCADE");
      table.string("title").notNullable();
      table.string("message").notNullable();
      table.jsonb("details").notNullable();
      table.boolean("is_read").defaultTo(false);
      table.timestamps(true, true);
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable(TABLE.NOTIFICATIONS);
}
