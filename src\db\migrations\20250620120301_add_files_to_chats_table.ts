import type { K<PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable(TABLE.CHATS, (table) => {
    table.string('file_url').nullable();
    table.string('file_name').nullable();
    table.string('file_type').nullable();
    table.integer('file_size').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable(TABLE.CHATS, (table) => {
    table.dropColumn('file_url');
    table.dropColumn('file_name');
    table.dropColumn('file_type');
    table.dropColumn('file_size');
  });
}