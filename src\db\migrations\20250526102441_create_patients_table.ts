// migrations/20250526122000_create_patients_table.ts

import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable("patients", (table) => {
    table.increments("id").primary();

    table.integer("doctor_id").unsigned().notNullable()
      .references("id").inTable("users").onDelete("CASCADE");

    table.string("first_name").notNullable();
    table.string("last_name").notNullable();
    table.string("email").notNullable().unique();
    table.date("dob").nullable();
    table.enum("gender", ["male", "female", "other"]).nullable();

    // Optional office addresses
    table.string("ship_to_office").nullable();
    table.string("bill_to_office").nullable();

    // plan_id without foreign key, to avoid missing plans table
    table.integer("plan_id").unsigned().nullable();

    // Clinical conditions and general notes
    table.text("clinical_conditions").nullable();
    table.text("general_notes").nullable();

    table.boolean("is_active").defaultTo(true);

    table.timestamps(true, true); // created_at, updated_at
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTableIfExists("patients");
}
