import { Knex } from "knex";

exports.up = async function (knex: Knex): Promise<void> {
  await knex.schema.createTable("refinements", (table) => {
    table.increments("id").primary();
    table
      .integer("patient_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("patients")
      .onDelete("CASCADE");

    table.jsonb("initial_v").notNullable().defaultTo(knex.raw(`'{}'::jsonb`));
    table.timestamp("created_at").notNullable().defaultTo(knex.fn.now());
    table.timestamp("updated_at").notNullable().defaultTo(knex.fn.now()); // just initial default
  });

  await knex.schema.alterTable("refinements", (table) => {
    table.index("patient_id", "idx_refinements_patient_id");
  });
};

exports.down = async function (knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("refinements");
};
