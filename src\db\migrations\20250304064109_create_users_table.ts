import type { <PERSON><PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";
import { UserRole } from "../../utils/enums/users.enum";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable(TABLE.USERS, (table) => {
    table.increments("id").primary();
    table.string("first_name").nullable();
    table.string("last_name").nullable();
    table.string("email").unique().notNullable();
    table.string("password").nullable();
    table.string("role").defaultTo(UserRole);
    table.string("profile_image").nullable();
    table.integer("zip_code").nullable();
    table.string("social_id").nullable();
    table.boolean("is_active").defaultTo(true);
    table.boolean("is_verified").defaultTo(false);
    table.boolean("is_deleted").defaultTo(false);
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable(TABLE.USERS);
}
