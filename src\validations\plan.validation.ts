// src/validations/plan.validation.ts
import { z } from "zod";

// Preprocess duration_years: treat empty string or null as undefined, otherwise coerce string to number
const preprocessDuration = z.preprocess((val) => {
  if (val === "" || val === null || val === undefined) {
    return undefined;
  }
  if (typeof val === "string") {
    const parsed = Number(val);
    return isNaN(parsed) ? val : parsed;
  }
  return val;
}, z.number().int({ message: "Duration must be an integer" })
     .min(1, { message: "Duration must be at least 1 year" })
     .max(10, { message: "Duration cannot exceed 10 years" })
     .optional()
);

export const createPlanSchema = z
  .object({
    name: z
      .string()
      .min(3, { message: "Plan name must be at least 3 characters" })
      .max(100, { message: "Plan name must not exceed 100 characters" }),
    type: z.enum(["aligner", "retainer"]),
    duration_years: preprocessDuration,
  })
  .superRefine((data, ctx) => {
    if (data.type === "aligner" && data.duration_years == null) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "duration_years is required for aligner plans",
        path: ["duration_years"],
      });
    }
    if (data.type === "retainer" && data.duration_years != null) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "duration_years must not be provided for retainer plans",
        path: ["duration_years"],
      });
    }
  });

export const updatePlanSchema = z
  .object({
    name: z
      .string()
      .min(3, { message: "Plan name must be at least 3 characters" })
      .max(100, { message: "Plan name must not exceed 100 characters" })
      .optional(),
    type: z.enum(["aligner", "retainer"]).optional(),
    duration_years: preprocessDuration,
  })
  .superRefine((data, ctx) => {
    if (data.type === "aligner" && data.duration_years == null) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "duration_years is required when changing to aligner",
        path: ["duration_years"],
      });
    }
    if (data.type === "retainer" && data.duration_years != null) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "duration_years cannot be used with retainer plans",
        path: ["duration_years"],
      });
    }
  });
