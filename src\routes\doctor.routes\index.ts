import { Router } from "express";
import { authMiddleware } from "../../middlewares/authMiddleware";
import { storageData } from "../../utils/services/multer";
import {
  createEmployee,
  deleteEmployee,
  getEmployees,
  updateEmployee,
  addDoctorAddress,
  getDoctorAddresses,
  updateDoctorAddress,
  deleteDoctorAddress,
  savePatientStep,
  getPatientById,
  getAllPatientsForDoctor,
  uploadCbctFile,
  uploadRefinements,
  createAlignerReplacement,
  requestRetainer,
  reviewSharedLinkVersion,
} from "../../controller/doctor.controller";

const router = Router();
const upload = storageData("patients");

// Employee management routes
router.post("/employees", authMiddleware, upload.none(), createEmployee);
router.get("/employees", authMiddleware, getEmployees);
router.put(
  "/employees/:employeeId",
  authMiddleware,
  upload.none(),
  updateEmployee
);
router.delete("/employees/:employeeId", authMiddleware, deleteEmployee);

// Address management routes
router.post("/addresses", authMiddleware, upload.none(), addDoctorAddress);
router.get("/addresses", authMiddleware, getDoctorAddresses);
router.put(
  "/addresses/:addressId",
  authMiddleware,
  upload.none(),
  updateDoctorAddress
);
router.delete("/addresses/:addressId", authMiddleware, deleteDoctorAddress);
router.post(
  "/patients",
  authMiddleware,
  upload.fields([
    { name: "stlFile1", maxCount: 1 },
    { name: "stlFile2", maxCount: 1 },
    { name: "cbctFile", maxCount: 1 },
    { name: "profileRepose", maxCount: 1 },
    { name: "buccalRight", maxCount: 1 },
    { name: "buccalLeft", maxCount: 1 },
    { name: "frontalRepose", maxCount: 1 },
    { name: "frontalSmiling", maxCount: 1 },
    { name: "labialAnterior", maxCount: 1 },
    { name: "occlussalLower", maxCount: 1 },
    { name: "occlussalUpper", maxCount: 1 },
    { name: "radioGraph1", maxCount: 1 },
    { name: "radioGraph2", maxCount: 1 },
  ]),
  savePatientStep
);
router.post(
  "/retainer/patients",
  authMiddleware,
  upload.fields([
    { name: "stlFile1", maxCount: 1 },
    { name: "stlFile2", maxCount: 1 },
    { name: "cbctFile", maxCount: 1 },
    { name: "profileRepose", maxCount: 1 },
    { name: "buccalRight", maxCount: 1 },
    { name: "buccalLeft", maxCount: 1 },
    { name: "frontalRepose", maxCount: 1 },
    { name: "frontalSmiling", maxCount: 1 },
    { name: "labialAnterior", maxCount: 1 },
    { name: "occlussalLower", maxCount: 1 },
    { name: "occlussalUpper", maxCount: 1 },
  ]),
  savePatientStep
);
router.post(
  "/patients/:patientId/cbct",
  authMiddleware,
  upload.single("cbctFile"),
  uploadCbctFile
);
router.post(
  "/patients/:patientId/refinements",
  authMiddleware,
  upload.fields([
    { name: "upperImpression", maxCount: 1 },
    { name: "lowerImpression", maxCount: 1 },
    { name: "profileRepose", maxCount: 1 },
    { name: "buccalRight", maxCount: 1 },
    { name: "buccalLeft", maxCount: 1 },
    { name: "frontalRepose", maxCount: 1 },
    { name: "frontalSmiling", maxCount: 1 },
    { name: "labialAnterior", maxCount: 1 },
    { name: "occlussalLower", maxCount: 1 },
    { name: "occlussalUpper", maxCount: 1 },
    { name: "radioGraph1", maxCount: 1 },
    { name: "radioGraph2", maxCount: 1 },
  ]),
  uploadRefinements
);
router.post(
  "/patients/aligner-replacements",
  authMiddleware,
  upload.none(),
  createAlignerReplacement
);

router.post(
  "/patients/4dgraphic_retainer",
  authMiddleware,
  upload.fields([
    { name: "stlFile1", maxCount: 1 },
    { name: "stlFile2", maxCount: 1 },
    { name: "cbctFile", maxCount: 1 },
    { name: "profileRepose", maxCount: 1 },
    { name: "buccalRight", maxCount: 1 },
    { name: "buccalLeft", maxCount: 1 },
    { name: "frontalRepose", maxCount: 1 },
    { name: "frontalSmiling", maxCount: 1 },
    { name: "labialAnterior", maxCount: 1 },
    { name: "occlussalLower", maxCount: 1 },
    { name: "occlussalUpper", maxCount: 1 },
    { name: "radioGraph1", maxCount: 1 },
    { name: "radioGraph2", maxCount: 1 },
  ]),
  requestRetainer
);
router.get("/patients/:id", authMiddleware, upload.none(), getPatientById);
router.get("/patients", authMiddleware, upload.none(), getAllPatientsForDoctor);
router.post(
  "/patients/shared-review/:versionId",
  authMiddleware,
  upload.none(),
  reviewSharedLinkVersion
);
export default router;
