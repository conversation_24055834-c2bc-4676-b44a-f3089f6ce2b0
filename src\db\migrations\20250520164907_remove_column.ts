import type { K<PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";


export async function up(knex: Knex): Promise<void> {
    return knex.schema.alterTable(TABLE.USERS, (table) => {
    // Add new columns
    table.dropColumn("zip_code");
    table.dropColumn("social_id");
    table.string("username").nullable();
  });
}


export async function down(knex: Knex): Promise<void> {
    return knex.schema.alterTable(TABLE.USERS, (table) => {
    // Remove the columns added in the up function
    table.dropColumn("zip_code");
    table.dropColumn("social_id");
    
    // Re-add columns that were removed (uncomment if needed)
    // table.integer("zip_code").nullable();
  });
}

