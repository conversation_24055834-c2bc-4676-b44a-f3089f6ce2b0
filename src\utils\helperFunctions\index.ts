import db from "../../config/db";
import { TABLE } from "../Database/table";


export const saveMessage = async (
  sender_id: number,
  receiver_id: number,
  patient_id: number,
  message: string
) => {
  try {
    let result;
    if (db.client.config.client === "pg") {
      // PostgreSQL
      [result] = await db(TABLE.CHATS)
        .insert({ sender_id, receiver_id, patient_id, message })
        .returning("*");
    } else {
      // MySQL/SQLite
      const [id] = await db(TABLE.CHATS).insert({ 
        sender_id, 
        receiver_id, 
        patient_id, 
        message 
      });
      result = await db(TABLE.CHATS).where({ id }).first();
    }
    return result;
  } catch (err) {
    console.error("Error saving message:", err);
    throw err;
  }
};
export const fetchNotifications = async (
    userId: string,
    page: number = 1,
    pageSize: number = 10,
    filterType?: string
) => {
    const offset = (page - 1) * pageSize;

    let query = db(TABLE.NOTIFICATIONS)
        .where({ receiver_id: Number(userId) })
        .orderBy("created_at", "desc")
        .offset(offset)
        .limit(pageSize);

    if (filterType === "unread") {
        query = query.andWhere({ isRead: false });
    }

    const [notifications, totalCountResult] = await Promise.all([
        query,
        db(TABLE.NOTIFICATIONS)
            .where({ receiver_id: Number(userId) })
            .modify((qb) => {
                if (filterType === "unread") {
                    qb.andWhere({ isRead: false });
                }
            })
            .count("id as count")
            .first(),
    ]);

    const totalCount = Number(totalCountResult?.count ?? 0);
    const totalPages = Math.ceil(totalCount / pageSize);

    return {
        notifications,
        pagination: {
            totalItems: totalCount,
            totalPages,
            currentPage: page,
            pageSize,
        },
    };
};