{"paths": {"/doctor/addresses": {"post": {"tags": ["Doctor Management"], "summary": "Add new address for doctor", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "required": ["clinic_name", "street_address", "city"], "properties": {"clinic_name": {"type": "string", "example": "Main Street Dental"}, "street_address": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "postal_code": {"type": "string", "example": "10001"}, "phone_number": {"type": "string", "example": "+1234567890"}}}}}}, "responses": {"201": {"description": "Address added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressResponse"}}}}, "400": {"description": "Validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Unauthorized - Only doctors can add addresses", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized - Invalid or missing token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "get": {"tags": ["Doctor Management"], "summary": "Get all addresses for doctor", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Addresses retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Addresses retrieved"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Address"}}}}}}}, "403": {"description": "Unauthorized - Only doctors can view addresses", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/doctor/addresses/{addressId}": {"put": {"tags": ["Doctor Management"], "summary": "Update doctor address", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "addressId", "required": true, "schema": {"type": "integer"}, "description": "ID of address to update"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/AddressInput"}}}}, "responses": {"200": {"description": "Address updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressResponse"}}}}, "404": {"description": "Address not found or doesn't belong to doctor", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["Doctor Management"], "summary": "Delete doctor address", "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "addressId", "required": true, "schema": {"type": "integer"}, "description": "ID of address to delete"}], "responses": {"200": {"description": "Address deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Address deleted successfully"}}}}}}, "404": {"description": "Address not found or doesn't belong to doctor", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"Address": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "doctor_id": {"type": "integer", "example": 1}, "clinic_name": {"type": "string", "example": "Main Street Dental"}, "street_address": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "postal_code": {"type": "string", "example": "10001"}, "phone_number": {"type": "string", "example": "+1234567890"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "AddressInput": {"type": "object", "required": ["clinic_name", "street_address", "city"], "properties": {"clinic_name": {"type": "string", "example": "Main Street Dental"}, "street_address": {"type": "string", "example": "123 Main St"}, "city": {"type": "string", "example": "New York"}, "postal_code": {"type": "string", "example": "10001"}, "phone_number": {"type": "string", "example": "+1234567890"}}}, "AddressResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string"}, "data": {"$ref": "#/components/schemas/Address"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}}}}}}