import { Request, Response, NextFunction } from "express";
import { JwtPayload } from "jsonwebtoken";
import { verifyToken } from "../utils/services/jwt";
import db from "../config/db";
import { TABLE } from "../utils/Database/table";
import { sendResponse } from "../utils/helperFunctions/responseHelper";
import { UserRole } from "../utils/enums/users.enum";

export const authMiddleware = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const token = req.header("Authorization")?.replace("Bearer ", "");

      if (!token) {
        sendResponse(res, 401, "Unauthorized! please login first.", false);
        return;
      }

      const decoded = verifyToken(token);
      const userId = (decoded as JwtPayload).id;

      // Fetch user details with role information
      const user = await db(`${TABLE.USERS} as u`)
        .leftJoin(`${TABLE.ROLES} as r`, 'u.role_id', 'r.id')
        .select(
          'u.*',
          'r.role_name as role'
        )
        .where('u.id', userId)
        .first();

      if (!user) {
        sendResponse(res, 404, "Account not found", false);
        return;
      }

      if (!user.is_verified) {
        sendResponse(res, 403, "Your account is not verified", false);
        return;
      }

      if (!user.is_active) {
        sendResponse(res, 403, "Your account has been disabled by the administrator", false);
        return;
      }

      req.user = user;
      next();
    } catch (error: any) {
      console.error("JWT verification error:", error.message);
      sendResponse(res, 401, "Unauthorized! please login first.", false);
      return;
    }
  };
