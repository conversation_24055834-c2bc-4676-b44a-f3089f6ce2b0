// socket.ts
import { Server as HttpServer } from "http";
import { Server as SocketServer, Socket } from "socket.io";
import { setupChat } from "../controller/chat.controller";
import { fetchNotifications } from "../utils/helperFunctions";

let io: SocketServer;
export let users: {
  userId: string;
  socketIds: string[];
}[] = [];

export const initSocket = (server: HttpServer) => {
  io = new SocketServer(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
  },
  // allow up to 100 MB per payload
  maxHttpBufferSize: 100 * 1024 * 1024,
  // explicitly allow polling as well as websocket transport
  transports: ["websocket", "polling"],
});

  io.on("connection", (socket: Socket) => {
    console.log(`Socket connected: ${socket.id}`);

    // Add user to memory and join room
    socket.on("addUser", (userId: string) => {
      socket.data.userId = userId;

      const existingUser = users.find((user) => user.userId === userId);
      if (existingUser) {
        if (!existingUser.socketIds.includes(socket.id)) {
          existingUser.socketIds.push(socket.id);
        }
      } else {
        users.push({ userId, socketIds: [socket.id] });
        io.emit("userStatusChange", { userId, status: "online" });
      }

      socket.join(`user_${userId}`);
      io.emit("getUsers", users);
    });

    socket.on(
      "notifications",
      async ({ userId, page = 1, pageSize = 10, filterType }) => {
        if (!userId) {
          return socket.emit("responseError", "User ID is required");
        }

        try {
          const data = await fetchNotifications(userId, page, pageSize, filterType);
          io.to(`user_${userId}`).emit("getNotifications", data);
        } catch (error) {
          console.error("Notification fetch error:", error);
          socket.emit("responseError", "Failed to fetch notifications");
        }
      }
    );

    setupChat(io, socket);

    socket.on("disconnect", () => {
      const user = users.find((u) => u.socketIds.includes(socket.id));
      if (user) {
        user.socketIds = user.socketIds.filter((id) => id !== socket.id);
        if (user.socketIds.length === 0) {
          io.emit("userStatusChange", { userId: user.userId, status: "offline" });
          users = users.filter((u) => u.userId !== user.userId);
        }
      }
      io.emit("getUsers", users);
      console.log(`Socket disconnected: ${socket.id}`);
    });
  });
};

export const getIO = () => {
  if (!io) throw new Error("Socket.io not initialized");
  return io;
};
