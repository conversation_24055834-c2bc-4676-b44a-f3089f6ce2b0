import express from "express";
import { createPlan, destroyPlanById, getAllPlans, getPlanById, updatePlan } from "../../../controller/admin/plan.controller";
import { storageData } from "../../../utils/services/multer";
import { authAdminMiddleware } from "../../../middlewares/authAdminMiddleware";


const router = express.Router();
const upload = storageData("plan");
router.post("/plan", authAdminMiddleware, upload.none(), createPlan);
router.get("/plan", authAdminMiddleware, upload.none(), getAllPlans);
router.get("/plan/:id", authAdminMiddleware, upload.none(), getPlanById);
router.patch("/plan/:id", authAdminMiddleware, upload.none(), updatePlan);
router.delete("/plan/:id", authAdminMiddleware, upload.none(), destroyPlanById);

export default router;
