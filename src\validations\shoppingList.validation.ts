import { z } from "zod";

export const shoppingListSchema = z.object({
  user_id: z.number({ required_error: "User ID is required" }),
  name: z.string({ required_error: "Shopping list name is required" }).min(1, "Shopping list name cannot be empty"),
  description: z.string({ required_error: "Description is required" }).min(1, "Description cannot be empty"),
  is_favourite: z.boolean().optional(),
});
