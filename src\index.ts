import express, { Express, Request, Response } from "express";
import dotenv from "dotenv";
import cors from "cors";
import http from "http";
import path from "path";

import { morganConfig } from "./config/morgan";
import corsOptions from "./config/cors";
import { dbConnection } from "./config/dbConnection";
import Routes from "./routes";
import { combinedSwagger, swaggerUi } from "./config/swagger.config";
import { initSocket } from "./config/socket";

dotenv.config();

const app: Express = express();
const port = process.env.PORT || 8000;

morganConfig(app);
app.use(cors(corsOptions));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// serve everything in public/ at the web root
app.use(express.static(path.join(__dirname, "../public")));

// ── NEW: serve chat uploads from public/chat under /chat ───────────────────
app.use(
  "/chat",
  express.static(path.join(__dirname, "../public/chat"))
);

dbConnection();

app.use("/api/v1", Routes);

app.use(
  "/api-docs",
  swaggerUi.serve,
  swaggerUi.setup(combinedSwagger, {
    swaggerOptions: { docExpansion: "none", tagsSorter: () => null },
  })
);

app.get("/", (req: Request, res: Response) => {
  res.send(
    `<h3>${(process.env.APP_NAME || "Application")
      .toUpperCase()} ${(process.env.NODE_ENV || "production")
      .toUpperCase()} SERVER IS RUNNING...</h3>`
  );
});

app.use((req: Request, res: Response) => {
  res.status(404).json({ status: 404, success: false, message: "Route not found" });
});

const server = http.createServer(app);
initSocket(server);

server.listen(port, () => {
  console.log(`[server]: Server is running at http://localhost:${port}`);
});
