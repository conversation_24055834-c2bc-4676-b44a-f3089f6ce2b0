import { <PERSON><PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";
import { UserRole } from "../../utils/enums/users.enum";

export async function seed(knex: Knex): Promise<void> {
  // Clear existing entries
  await knex(TABLE.PERMISSIONS).del();
  await knex(TABLE.MODULES).del();

  // Insert modules
  const modules = await knex(TABLE.MODULES).insert([
    { name: "patients", description: "Patient records management" },
    { name: "appointments", description: "Appointment scheduling" },
    { name: "inventory", description: "Inventory management" },
    { name: "billing", description: "Billing and payments" },
    { name: "reports", description: "Reports and analytics" },
    { name: "settings", description: "System settings" }
  ]).returning("id");

  // Get role IDs
  const roles = await knex(TABLE.ROLES).select("id", "role_name");
  const roleMap: Record<string, number> = {};
  
  roles.forEach(role => {
    roleMap[role.role_name] = role.id;
  });

  // Create permissions for each role
  const permissionsToInsert = [];

  // Super Admin has all permissions on all modules
  if (roleMap[UserRole.SUPERADMIN]) {
    for (const module of modules) {
      permissionsToInsert.push({
        role_id: roleMap[UserRole.SUPERADMIN],
        module_id: module.id,
        create: 1,
        list: 1,
        view: 1,
        edit: 1,
        delete: 1
      });
    }
  }

  // Admin has all permissions except delete on most modules
  if (roleMap[UserRole.ADMIN]) {
    for (const module of modules) {
      permissionsToInsert.push({
        role_id: roleMap[UserRole.ADMIN],
        module_id: module.id,
        create: 1,
        list: 1,
        view: 1,
        edit: 1,
        delete: module.name === 'settings' ? 0 : 1 // No delete permission on settings
      });
    }
  }

  // Doctor has limited permissions
  if (roleMap[UserRole.DOCTOR]) {
    for (const module of modules) {
      const isRestrictedModule = module.name === 'settings';
      
      permissionsToInsert.push({
        role_id: roleMap[UserRole.DOCTOR],
        module_id: module.id,
        create: isRestrictedModule ? 0 : 1,
        list: 1,
        view: 1,
        edit: isRestrictedModule ? 0 : 1,
        delete: isRestrictedModule ? 0 : 1
      });
    }
  }

  // Employee has very limited permissions
  if (roleMap[UserRole.EMPLOYEE]) {
    for (const module of modules) {
      const isRestrictedModule = ['settings', 'reports'].includes(module.name);
      
      permissionsToInsert.push({
        role_id: roleMap[UserRole.EMPLOYEE],
        module_id: module.id,
        create: isRestrictedModule ? 0 : 1,
        list: 1,
        view: 1,
        edit: isRestrictedModule ? 0 : 1,
        delete: 0 // Employees can't delete anything
      });
    }
  }

  // Insert all permissions
  if (permissionsToInsert.length > 0) {
    await knex(TABLE.PERMISSIONS).insert(permissionsToInsert);
  }

  console.log(`Seeded ${modules.length} modules and ${permissionsToInsert.length} permissions`);
}
