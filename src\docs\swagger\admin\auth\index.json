{"paths": {"/auth/login-admin": {"post": {"summary": "Admin login", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "Admin@123"}}, "required": ["email", "password"]}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"accessToken": {"type": "string", "example": "jwt_token_here"}, "user": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "Admin"}, "last_name": {"type": "string", "example": "User"}, "email": {"type": "string", "example": "<EMAIL>"}, "profile_image": {"type": "string", "example": "http://localhost:5000/images/profile.jpg"}, "role": {"type": "string", "example": "SUPERADMIN"}}}}}}}}, "400": {"description": "Invalid email or password"}, "403": {"description": "Unauthorized or inactive account"}, "500": {"description": "Internal server error"}}}}}}