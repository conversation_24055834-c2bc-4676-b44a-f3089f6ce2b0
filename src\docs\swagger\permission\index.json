{"paths": {"/permissions/me": {"get": {"summary": "Get permissions for the currently logged-in user", "tags": ["Permissions"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "User permissions retrieved successfully", "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User permissions retrieved successfully"}, "data": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "string", "example": "550e8400-e29b-41d4-a716-446655440000"}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "role": {"type": "string", "example": "Doctor"}}}, "modules": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Patients"}, "description": {"type": "string", "example": "Patient management module"}, "permissions": {"type": "object", "properties": {"create": {"type": "boolean", "example": true}, "list": {"type": "boolean", "example": true}, "view": {"type": "boolean", "example": true}, "edit": {"type": "boolean", "example": true}, "delete": {"type": "boolean", "example": false}}}}}}}}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token"}, "500": {"description": "Internal server error"}}}}, "/permissions/role/{roleId}": {"get": {"summary": "Get permissions for a specific role", "tags": ["Permissions"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "roleId", "required": true, "schema": {"type": "integer"}, "description": "ID of the role"}], "responses": {"200": {"description": "Role permissions retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Role permissions retrieved successfully"}, "data": {"type": "object", "properties": {"role": {"type": "object", "properties": {"id": {"type": "integer", "example": 2}, "name": {"type": "string", "example": "Doctor"}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 5}, "moduleId": {"type": "integer", "example": 1}, "moduleName": {"type": "string", "example": "Patients"}, "moduleDescription": {"type": "string", "example": "Patient management module"}, "permissions": {"type": "object", "properties": {"create": {"type": "boolean", "example": true}, "list": {"type": "boolean", "example": true}, "view": {"type": "boolean", "example": true}, "edit": {"type": "boolean", "example": true}, "delete": {"type": "boolean", "example": false}}}}}}}}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token"}, "404": {"description": "Role not found"}, "500": {"description": "Internal server error"}}}}, "/permissions/role": {"post": {"summary": "Assign permissions to a role", "description": "Allows administrators to assign permissions to a specific role. Cannot modify superAdmin role permissions.", "tags": ["Permissions"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "required": ["roleId", "modulePermissions"], "properties": {"roleId": {"type": "string", "example": "8", "description": "ID of the role to assign permissions to"}, "modulePermissions": {"type": "string", "description": "JSON string containing array of module permissions", "example": "[{\"moduleId\":1,\"permissions\":{\"create\":false,\"list\":true,\"view\":true,\"edit\":true,\"delete\":false}}]"}}}}}}, "responses": {"200": {"description": "Permissions assigned successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Permissions assigned successfully to Doctor role"}}}}}}, "400": {"description": "Bad request - invalid input data", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 400}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "roleId and modulePermissions are required"}}}}}}, "401": {"description": "Unauthorized - Invalid or missing token"}, "403": {"description": "Forbidden - User doesn't have admin privileges or attempting to modify superAdmin role"}, "404": {"description": "Role not found"}, "500": {"description": "Internal server error"}}}}, "/permissions/employee/{employeeId}": {"get": {"summary": "Get permissions for a specific employee (doctor only)", "tags": ["Permissions"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "employeeId", "required": true, "schema": {"type": "string"}, "description": "ID of the employee"}], "responses": {"200": {"description": "Employee permissions retrieved successfully"}, "401": {"description": "Unauthorized - Invalid or missing token"}, "403": {"description": "Forbidden - User is not a doctor or employee doesn't belong to doctor"}, "404": {"description": "Employee not found"}, "500": {"description": "Internal server error"}}}}, "/permissions/modules": {"get": {"summary": "Get all available modules", "description": "Retrieves all system modules ordered by name in ascending order", "tags": ["Permissions"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> retrieved successfully"}, "data": {"type": "array", "items": {}}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "integer", "example": 500}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Failed to retrieve modules"}}}}}}}}}}}