import type { K<PERSON> } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TABLE.ROLE_PERMISSIONS, (table) => {
    table.increments("id").primary();
    table.integer("role_id").unsigned().notNullable();
    table.integer("permission_id").unsigned().notNullable();
    table.foreign("role_id").references("id").inTable(TABLE.ROLES);
    table.foreign("permission_id").references("id").inTable(TABLE.PERMISSIONS);
    table.unique(["role_id", "permission_id"]);
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TABLE.ROLE_PERMISSIONS);
}