import nodemailer from "nodemailer";
import { Response } from "express";
import db from "../../../config/db";
import { TABLE } from "../../Database/table";
import { generateOTP } from "../../helperFunctions/otpGenerator";
import transporter from ".";
import { response } from "../../response";
import { User } from "../../types/auth";

// Function to send OTP for account verification
export const sendForgotPasswordEmail = async (
    user: User,
  res: Response
): Promise<void> => {
  const otp = await generateOTP();

  // Save OTP for verification
  await db(TABLE.PASSWORD_RESETS).insert({
    email: user.email,
    token: otp,
    created_at: new Date(),
  });

  // Email options
  const mailOptions: nodemailer.SendMailOptions = {
    from: `"Orthodontic Lab Team" <<EMAIL>>`,
    to: user.email,
    subject: "Reset Your Password",
    html: `
      <html>
        <body style="font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 0;">
          <div style="max-width: 600px; margin: 50px auto; padding: 20px; background-color: #fff; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            <h2 style="color: #333; text-align: center;">Reset Your Password</h2>
            <p style="font-size: 16px; color: #555;">Hi,</p>
            <p style="font-size: 16px; color: #555;">
              You recently requested to reset your password for your Orthodontic Lab account.
              Use the OTP below to proceed with resetting your password:
            </p>
            <h2 style="text-align: center; color: #4CAF50;">${otp}</h2>
            <p style="font-size: 14px; color: #555; text-align: center;">This OTP will expire in 10 minutes.</p>
            <p style="font-size: 14px; color: #888; text-align: center;">If you did not request this, please ignore this email.</p>
          </div>
        </body>
      </html>
    `,
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log("Password reset email sent successfully!");
    response(res, 201, "Password reset email sent successfully.");
  } catch (error) {
    console.error("Error sending password reset email:", error);
    res.status(500).json({
      message: "Failed to send password reset email. Please try again later.",
    });
  }
};
