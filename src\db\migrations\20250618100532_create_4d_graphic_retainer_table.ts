import { <PERSON><PERSON> } from "knex"

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("four_d_graphy_retainers", (table) => {
    table.increments("id").primary()
    table
      .integer("patient_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("patients")
      .onDelete("CASCADE")

    table.text("other_details").nullable()

    // Separate columns for each STL / scan file
    table.string("stl_file1").nullable()
    table.string("stl_file2").nullable()

    // Separate columns for additional scan photos
    table.string("cbct_file").nullable()
    table.string("profile_repose").nullable()
    table.string("buccal_right").nullable()
    table.string("buccal_left").nullable()
    table.string("frontal_repose").nullable()
    table.string("frontal_smiling").nullable()
    table.string("labial_anterior").nullable()
    table.string("occlusal_lower").nullable()
    table.string("occlusal_upper").nullable()
    table.string("radiograph1").nullable()
    table.string("radiograph2").nullable()

    table
      .timestamp("created_at", { useTz: true })
      .notNullable()
      .defaultTo(knex.fn.now())
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("four_d_graphy_retainers")
}
