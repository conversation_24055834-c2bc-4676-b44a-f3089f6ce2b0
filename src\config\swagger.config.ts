import swaggerUi from "swagger-ui-express";
import fs from "fs";
import path from "path";
import deepmerge from "deepmerge";
// Define types for our tag structure
interface Tag {
  name: string;
  description: string;
}
interface TagsFile {
  tags: Tag[];
}
// Define OpenAPI document structure
interface OpenAPIDocument {
  openapi: string;
  info: {
    title: string;
    version: string;
    description: string;
  };
  servers: Array<{
    url: string;
    description: string;
  }>;
  components?: {
    securitySchemes?: {
      [key: string]: any;
    };
  };
  security?: Array<{
    [key: string]: string[];
  }>;
  tags?: Tag[];
  paths?: {
    [path: string]: any;
  };
  [key: string]: any;
}
// Load individual Swagger JSON files
const adminAuthSwagger = JSON.parse(
  fs.readFileSync(
    path.join(__dirname, "../docs/swagger/admin/auth/index.json"),
    "utf-8"
  )
);
const userAuthSwagger = JSON.parse(
  fs.readFileSync(
    path.join(__dirname, "../docs/swagger/user/auth/index.json"),
    "utf-8"
  )
);
const doctorEmployeeSwagger = JSON.parse(
  fs.readFileSync(
    path.join(__dirname, "../docs/swagger/doctor/employee/index.json"),
    "utf-8"
  )
);
const doctorAddressSwagger = JSON.parse(
  fs.readFileSync(
    path.join(__dirname, "../docs/swagger/doctor/address/index.json"),
    "utf-8"
  )
);
const permissionSwagger = JSON.parse(
  fs.readFileSync(
    path.join(__dirname, "../docs/swagger/permission/index.json"),
    "utf-8"
  )
);
const doctorPatientSwagger = JSON.parse(
  fs.readFileSync(
    path.join(__dirname, "../docs/swagger/doctor/patient/index.json"),
    "utf-8"
  )
);
const adminPlanSwagger = JSON.parse(
  fs.readFileSync(
    path.join(__dirname, "../docs/swagger/admin/plan/index.json"),
    "utf-8"
  )
);
const specialistSwagger = JSON.parse(
  fs.readFileSync(
    path.join(__dirname, "../docs/swagger/specialist/index.json"),
    "utf-8"
  )
);

// Load tags from a separate JSON file with proper typing
const tagsFile = JSON.parse(
  fs.readFileSync(
    path.join(__dirname, "../docs/swagger/tags/index.json"),
    "utf-8"
  )
) as TagsFile;

const jsonDocsArray = [
  adminAuthSwagger,
  userAuthSwagger,
  doctorEmployeeSwagger,
  doctorAddressSwagger,
  permissionSwagger,
  doctorPatientSwagger,
  adminPlanSwagger,
  specialistSwagger
];

// Create base OpenAPI configuration
const baseConfig: OpenAPIDocument = {
  openapi: "3.0.0",
  info: {
    title: "Orthodontic-labs API",
    version: "1.0.0",
    description: "API documentation for Orthodontic-labs",
  },
  servers: [
    {
      url: process.env.SWAGGER_BASE_URL_DEV || "http://localhost:5000/api/v1",
      description: "Development server",
    },
    {
      url: process.env.SWAGGER_BASE_URL_PROD || "http://************:5000/api/v1",
      description: "Production server",
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
      },
    },
  },
  security: [
    {
      bearerAuth: [],
    },
  ],
  tags: tagsFile.tags,
  paths: {}
};

// Merge individual swagger files with custom merge options
const mergeOptions = {
  customMerge: (key: string) => {
    if (key === 'paths') {
      return (target: any, source: any) => ({...target, ...source});
    }
    return undefined; // Use default merging for other keys
  }
};

// Define a type for the merge result that only includes paths
type SwaggerPaths = {
  paths?: { [key: string]: any };
};

// Extract only the paths from each swagger file
const pathsToMerge: SwaggerPaths[] = jsonDocsArray.map(doc => ({
  paths: doc.paths || {}
}));

// First merge all the paths together
const mergedPaths = pathsToMerge.reduce<SwaggerPaths>(
  (acc, curr) => deepmerge(acc, curr, mergeOptions),
  { paths: {} }
);

// Then combine the merged paths with the base config
const combinedSwagger: OpenAPIDocument = {
  ...baseConfig,
  paths: mergedPaths.paths
};

// Set up Swagger UI with the combined documentation
export { swaggerUi, combinedSwagger };

// Express app setup (for reference, not to be included in the final file)
// app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(combinedSwagger, {
//   swaggerOptions: {
//     docExpansion: 'none',
//     // ...
//   }
// }));
