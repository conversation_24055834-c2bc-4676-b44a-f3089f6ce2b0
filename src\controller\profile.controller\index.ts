import { Request, Response } from "express";
import fs from "fs";
import async<PERSON>and<PERSON> from "../../middlewares/trycatch";
import db from "../../config/db";
import { TABLE } from "../../utils/Database/table";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";

export const getProfile = asyncHandler(async (req: Request, res: Response) => {
  try {
    // Check if the user exists
    const existingUser = await db(TABLE.USERS)
      .where({ id: req.user?.id, is_deleted: false })
      .first();

    if (!existingUser) {
      sendResponse(res, 400, "User account not found", false);
      return;
    }

    // Remove password from response
    delete existingUser.password;

    if (existingUser.profile_image) {
      existingUser.profile_image =
        process.env.BASE_URL + existingUser.profile_image;
    }

    sendResponse(res, 200, "Registered successfully", true, existingUser);
  } catch (error) {
    console.error("Error get profile:", error);
    return sendResponse(res, 500, "Failed to get profile.", false);
  }
});

export const updateProfile = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user?.id;

    try {
      // Fetch user
      const user = await db(TABLE.USERS)
        .where({ id: userId, is_deleted: false })
        .first();
      if (!user) {
        sendResponse(res, 404, "User not found", false);
        return;
      }

      // Extract updated fields
      const { first_name, last_name, zip_code } = req.body;

      // Prepare update payload
      const updateData: any = {
        first_name,
        last_name,
        zip_code,
        updated_at: new Date(),
      };

      // Handle profile image if uploaded
      if (req.file) {
        
        if (user.profile_image) {
          fs.unlinkSync(`public/${user.profile_image}`);
        }

        updateData.profile_image = "/user/" + req.file.filename; // or your custom path logic
      }

      await db(TABLE.USERS).where({ id: userId }).update(updateData);

      const updatedUser = await db(TABLE.USERS).where({ id: userId }).first();
      delete updatedUser.password;

      // Attach full image URL
      if (updatedUser.profile_image) {
        updatedUser.profile_image =
          process.env.BASE_URL + updatedUser.profile_image;
      }

      sendResponse(res, 200, "Profile updated successfully", true, updatedUser);
    } catch (err) {
      console.error("Error updating profile:", err);
      sendResponse(res, 500, "Failed to update profile", false, err);
    }
  }
);
