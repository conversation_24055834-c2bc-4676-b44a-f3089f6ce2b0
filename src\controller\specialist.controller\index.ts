import { Request, Response } from "express";
import bcrypt from "bcryptjs";
import db from "../../config/db";
import { TABLE } from "../../utils/Database/table";
import { UserRole } from "../../utils/enums/users.enum";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";
import asyncHandler from "../../middlewares/trycatch";
import validate from "../../validations";
import { createUserSchema } from "../../validations/user.validation";
import { sendEmployeeCredentialEmail } from "../../utils/services/nodemailer/employeeCredential";
import { addressSchema } from "../../validations/address.validation";

// ---------------------------------------------
// Review (Approve or Reject) a submitted patient file (Specialist only)
// ---------------------------------------------
// export const reviewPatientFile = asyncHandler(
//   async (req: Request, res: Response) => {
//     try {
//       // 1) Only specialists can call this
//       if (!req.user || req.user.role !== UserRole.SPECIALIST) {
//         return sendResponse(res, 403, "Only specialists can review patient files", false);
//       }

//       // 2) Extract patientFileId from URL params
//       const { patientFileId } = req.params;
//       if (!patientFileId) {
//         return sendResponse(res, 400, "patientFileId is required", false);
//       }

//       // 3) Extract action from form-data (must be "approve" or "reject")
//       const { action } = req.body;
//       if (action !== "approve" && action !== "reject") {
//         return sendResponse(res, 400, "Action must be either 'approve' or 'reject'", false);
//       }

//       // 4) Verify that the patient file exists and is still "pending"
//       const existingFile = await db(TABLE.REFINEMENTS)
//         .where({ id: patientFileId })
//         .first();
//       if (!existingFile) {
//         return sendResponse(res, 404, "Patient file not found", false);
//       }
//       if (existingFile.status !== "pending") {
//         return sendResponse(res, 400, "Only files with status 'pending' can be reviewed", false);
//       }

//       // 5) Branch based on action
//       let updatedFile: any;
//       if (action === "approve") {
//         // 5a) Approve logic
//         [updatedFile] = await db(TABLE.REFINEMENTS)
//           .where({ id: patientFileId })
//           .update({
//             status: "approved",
//             rejection_reason: null,
//             rejection_pdf_url: null,
//             reviewed_at: new Date(),
//           })
//           .returning([
//             "id",
//             "doctor_id",
//             "status",
//             "rejection_reason",
//             "rejection_pdf_url",
//             "reviewed_at",
//           ]);

//         // (Optional) Notify doctor that the file was approved
//         // await sendNotificationToDoctor({
//         //   doctorId: updatedFile.doctor_id,
//         //   message: `Your patient #PID${updatedFile.id} file has been approved by the specialist.`,
//         //   data: { patientFileId: updatedFile.id },
//         // });

//         return sendResponse(
//           res,
//           200,
//           "Patient file approved successfully",
//           true,
//           { patientFile: updatedFile }
//         );
//       } else {
//         // 5b) Reject logic
//         //   Ensure rejection_reason is provided
//         const { rejection_reason } = req.body;
//         if (!rejection_reason || rejection_reason.trim().length === 0) {
//           return sendResponse(res, 400, "Rejection reason is required when action is 'reject'", false);
//         }

//         //   Handle uploaded PDF (if any)
//         let rejectionPdfUrl: string | null = null;
//         if (req.file) {
//           rejectionPdfUrl = req.file.path;
//         }

//         [updatedFile] = await db(TABLE.REFINEMENTS)
//           .where({ id: patientFileId })
//           .update({
//             status: "rejected",
//             rejection_reason: rejection_reason.trim(),
//             rejection_pdf_url: rejectionPdfUrl,
//             reviewed_at: new Date(),
//           })
//           .returning([
//             "id",
//             "doctor_id",
//             "status",
//             "rejection_reason",
//             "rejection_pdf_url",
//             "reviewed_at",
//           ]);

//         // (Optional) Notify doctor that the file was rejected
//         // await sendNotificationToDoctor({
//         //   doctorId: updatedFile.doctor_id,
//         //   message: `Your patient #PID${updatedFile.id} file has been rejected by the specialist.`,
//         //   data: { patientFileId: updatedFile.id },
//         // });

//         return sendResponse(
//           res,
//           200,
//           "Patient file rejected successfully",
//           true,
//           { patientFile: updatedFile }
//         );
//       }
//     } catch (error: any) {
//       console.error("reviewPatientFile error:", error);
//       return sendResponse(res, 500, error.message || "Internal server error", false);
//     }
//   }
// );
export const reviewPatientVersion = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // 1) Only specialists can review
      if (!req.user || req.user.role !== UserRole.SPECIALIST) {
        return sendResponse(res, 403, "Only specialists can review versions", false);
      }

      // 2) Extract versionId
      const { versionId } = req.params;
      if (!versionId) {
        return sendResponse(res, 400, "versionId is required", false);
      }

      // 3) Extract action and optional fields
      const { action, reason, upper_steps, lower_steps, shared_link } = req.body;
      if (!["approve", "reject"].includes(action)) {
        return sendResponse(res, 400, "Action must be 'approve' or 'reject'", false);
      }

      // 4) Fetch the version
      const version = await db(TABLE.PATIENTS_VERSIONS)
        .where({ id: versionId })
        .first();
      if (!version) {
        return sendResponse(res, 404, "Version not found", false);
      }

      // Ensure only doctor-sent versions are reviewed
      if (version.status !== "sent_by_doctor") {
        return sendResponse(res, 400, "Only 'sent_by_doctor' versions can be reviewed", false);
      }

      // --- APPROVE FLOW ---
      if (action === "approve") {
        if (!shared_link) {
          return sendResponse(
            res,
            400,
            "Shared link is required to approve a version",
            false
          );
        }
        await db(TABLE.PATIENTS_VERSIONS)
          .where({ id: versionId })
          .update({
            status: "approved_by_specialist",
            approval_reason: reason || null,
            upper_steps: upper_steps || null,
            lower_steps: lower_steps || null,
            shared_link,
          });

        return sendResponse(res, 200, "Version approved successfully", true);
      }

      // --- REJECT FLOW ---
      if (shared_link) {
        return sendResponse(
          res,
          400,
          "Cannot share a link when rejecting a version",
          false
        );
      }

      if (!reason) {
        return sendResponse(res, 400, "Rejection reason is required", false);
      }

      await db(TABLE.PATIENTS_VERSIONS)
        .where({ id: versionId })
        .update({
          status: "rejected_by_specialist",
          rejection_reason: reason,
          upper_steps: upper_steps || null,
          lower_steps: lower_steps || null,
        });

      return sendResponse(res, 200, "Version rejected successfully", true);
    } catch (error: any) {
      console.error("reviewPatientVersion error:", error);
      return sendResponse(res, 500, error.message || "Internal server error", false);
    }
  }
);