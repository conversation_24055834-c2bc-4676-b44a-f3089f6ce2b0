// migrations/20250616_create_email_changes.ts
import { Knex } from "knex";
import { TABLE } from "../../utils/Database/table";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(TABLE.EMAIL_CHANGES, (table) => {
    table.increments("id").primary();

    table
      .integer("user_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable(TABLE.USERS)
      .onDelete("CASCADE");

    table.string("token", 255).notNullable().index(); // Changed from otp to token
    table.string("new_email", 255).notNullable();
    table
      .timestamp("expires_at", { useTz: true })
      .notNullable()
      .comment("When this token/link expires");

    table
      .boolean("is_verified")
      .notNullable()
      .defaultTo(false)
      .comment("Set to true when user clicks the link");

    table.timestamp("verified_at", { useTz: true }).nullable();

    table.timestamps(true, true);

    table.index(["user_id", "token"], "idx_email_changes_user_token"); // Updated index
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(TABLE.EMAIL_CHANGES);
}
