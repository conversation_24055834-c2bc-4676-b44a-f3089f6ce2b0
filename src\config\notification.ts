import * as admin from "firebase-admin";
import db from "../config/db";
import { TABLE } from "../utils/Database/table";
import { SendResponse } from "firebase-admin/lib/messaging/messaging-api";
import { users } from "../controller/notifications.controller";
import { fetchNotifications } from "../utils/helperFunctions";
import { getIO } from "./socket";

const serviceAccount = require("./kitchenkonnect-firebase-adminsdk-private-keys.json");

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://kitchenkonnect-d2103-default-rtdb.firebaseio.com",
});

interface CustomData {
  userId: number;
  receiverId: number;
  [key: string]: any;
}

export const sendNotification = async (
  title: string,
  msg: string,
  data: CustomData
): Promise<void> => {
  try {
    // 1. Store notification in DB
    await db(TABLE.NOTIFICATIONS).insert({
      title,
      message: msg,
      user_id: data.userId,
      receiver_id: data.receiverId,
      details: JSON.stringify(data),
    });

    // 2. Get all FCM tokens for the receiver
    const tokens = await db(TABLE.TOKENS)
      .where({ user_id: data.receiverId })
      .pluck("token");

    if (!tokens || tokens.length === 0) {
      console.log("No tokens found for user:", data.receiverId);
      return;
    }

    // 3. Build multicast message
    const message = {
      tokens,
      notification: {
        title,
        body: msg,
      },
    };

    const messaging = admin.messaging();
    const response = await messaging.sendEachForMulticast(message);

    // 4. Remove invalid tokens
    const failedTokens: string[] = [];
    response.responses.forEach((resp: SendResponse, idx: number) => {
      if (!resp.success) {
        failedTokens.push(tokens[idx]);
      }
    });

    if (failedTokens.length > 0) {
      await db(TABLE.TOKENS).whereIn("token", failedTokens).del();
      console.log("Removed invalid tokens:", failedTokens);
    }

    const allNotification = await fetchNotifications(String(data?.receiverId));

    const receiver = users.find(
      (user) => Number(user.userId) === Number(data?.receiverId)
    );

    if (receiver) {
      const io = getIO();
      receiver.socketIds.forEach((socketId) => {
        io.to(socketId).emit("getNotifications", allNotification);
      });
    }
  } catch (error) {
    console.error("Error sending push notification:", error);
  }
};
